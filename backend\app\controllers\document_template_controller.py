"""
Document Template controller module.
This module provides API endpoints for document templates.
"""
from flask import Blueprint, request, jsonify, send_file
from app.services.document_template_service import DocumentTemplateService
from app.utils.security import role_required, roles_required
from app.utils.rate_limit import rate_limit
from app.utils.file_validation import validate_file
from app.schemas.document_template_schema import document_template_schema
import logging
from marshmallow import ValidationError

# Define the blueprint for document template-related routes
document_template_bp = Blueprint("document_template", __name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the document template service
document_template_service = DocumentTemplateService()

@document_template_bp.route("", methods=["GET"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("60/minute")
def get_all_templates():
    """
    Get all document templates.

    Returns:
        JSON: List of document templates.
    """
    try:
        templates = document_template_service.get_all_templates()
        logger.info("Fetched all document templates")
        return jsonify(templates), 200
    except Exception as e:
        logger.error(f"Failed to fetch document templates: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>", methods=["GET"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("60/minute")
def get_template(template_id):
    """
    Get a specific document template by ID.

    Path Parameters:
        template_id (int): The ID of the template to retrieve.

    Returns:
        JSON: Template details.
    """
    try:
        template = document_template_service.get_template_by_id(template_id)
        if not template:
            return jsonify({"error": f"Template with ID {template_id} not found"}), 404
        logger.info(f"Fetched template with ID {template_id}")
        return jsonify(template), 200
    except Exception as e:
        logger.error(f"Failed to fetch template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/document-type/<string:document_type>", methods=["GET"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("60/minute")
def get_templates_by_document_type(document_type):
    """
    Get all document templates for a specific document type.

    Path Parameters:
        document_type (str): The document type to filter by.

    Returns:
        JSON: List of templates for the specified document type.
    """
    try:
        templates = document_template_service.get_templates_by_document_type(document_type)
        logger.info(f"Fetched templates for document type {document_type}")
        return jsonify(templates), 200
    except Exception as e:
        logger.error(f"Failed to fetch templates for document type {document_type}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("", methods=["POST"], strict_slashes=False)
@role_required("administrator")
@rate_limit("30/minute")
def create_template():
    """
    Create a new document template.

    Request Body: Form data with template details and file
    Returns: JSON: The created template
    """
    try:
        # Validate file
        if "file" not in request.files:
            return jsonify({"error": "No file part in the request"}), 400

        file = request.files["file"]
        if not file.filename or file.filename.strip() == "":
            return jsonify({"error": "No file selected"}), 400

        # Validate file using the file validation utility
        is_valid, message = validate_file(file)
        if not is_valid:
            logger.warning(f"File validation failed: {message}")
            return jsonify({"error": message}), 400

        # Parse form data
        template_data = {
            "name": request.form.get("name"),
            "document_type": request.form.get("document_type"),
            "description": request.form.get("description"),
            "created_by": request.current_user.id
        }

        # Validate the input data using the schema
        errors = document_template_schema.validate(template_data)
        if errors:
            logger.warning(f"Template validation failed: {errors}")
            return jsonify({"error": "Validation failed", "details": errors}), 400

        # Create the template
        template = document_template_service.create_template(template_data, file)
        logger.info(f"Created template: {template['name']}")
        return jsonify(template), 201
    except ValidationError as e:
        logger.warning(f"Template validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to create template: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>", methods=["PUT"], strict_slashes=False)
@role_required("administrator")
@rate_limit("30/minute")
def update_template(template_id):
    """
    Update an existing document template.

    Path Parameters:
        template_id (int): The ID of the template to update.

    Request Body: JSON with template details
    Returns: JSON: The updated template
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        template = document_template_service.update_template(template_id, data)
        logger.info(f"Updated template with ID {template_id}")
        return jsonify(template), 200
    except Exception as e:
        logger.error(f"Failed to update template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>", methods=["DELETE"], strict_slashes=False)
@role_required("administrator")
@rate_limit("30/minute")
def delete_template(template_id):
    """
    Delete a document template by ID.

    Path Parameters:
        template_id (int): The ID of the template to delete.

    Returns:
        JSON: Success message.
    """
    try:
        success = document_template_service.delete_template(template_id)
        if success:
            logger.info(f"Deleted template with ID {template_id}")
            return jsonify({"message": "Template deleted successfully"}), 200
        else:
            logger.warning(f"Failed to delete template with ID {template_id}")
            return jsonify({"error": "Failed to delete template"}), 500
    except Exception as e:
        logger.error(f"Failed to delete template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>/file", methods=["GET"], strict_slashes=False)
@rate_limit("60/minute")
def get_template_file(template_id):
    """
    Get the file for a specific document template.

    Path Parameters:
        template_id (int): The ID of the template to retrieve the file for.

    Returns:
        File: The template file.
    """
    try:
        # Allow file downloads without authentication
        # This is safe because we're only serving template files that should be accessible to all users
        return document_template_service.get_template_file(template_id)
    except Exception as e:
        logger.error(f"Failed to fetch template file for template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>/save", methods=["POST"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("30/minute")
def save_filled_template(template_id):
    """
    Save a filled template as a new document.

    Path Parameters:
        template_id (int): The ID of the template used.

    Request Body: Form data with filled template file and metadata
    Returns: JSON: The created document
    """
    try:
        # Validate file
        if "file" not in request.files:
            return jsonify({"error": "No file part in the request"}), 400

        file = request.files["file"]
        if not file.filename or file.filename.strip() == "":
            return jsonify({"error": "No file selected"}), 400

        # Get form data
        customer_id = request.form.get("customer_id")
        if not customer_id:
            return jsonify({"error": "Customer ID is required"}), 400

        try:
            customer_id = int(customer_id)
        except ValueError:
            return jsonify({"error": "Invalid customer ID"}), 400

        # Read file content
        file.seek(0)
        file_content = file.read()

        # Generate filename if not provided
        filename = file.filename or f"filled_template_{template_id}.docx"

        current_user = request.current_user
        uploaded_by = current_user.id

        # Save the filled template as a document
        document = document_template_service.save_filled_template(
            customer_id=customer_id,
            template_id=template_id,
            file_content=file_content,
            filename=filename,
            uploaded_by=uploaded_by
        )

        logger.info(f"Saved filled template {template_id} as document for customer {customer_id}")
        return jsonify(document), 201
    except Exception as e:
        logger.error(f"Failed to save filled template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>/process-with-signatures", methods=["POST"], strict_slashes=False)
@roles_required("monteur", "verkoper", "administrator")
@rate_limit("30/minute")
def process_template_with_signatures(template_id):
    """
    Process template data with signature handling.

    Path Parameters:
        template_id (int): The ID of the template to process.

    JSON Body:
        template_data (dict): Template field data including signatures.

    Returns:
        JSON: Processed template data.
    """
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        template_data = request.get_json()
        if not template_data:
            return jsonify({"error": "Template data is required"}), 400

        # Process the template data with signature handling
        processed_data = document_template_service.process_template_with_signatures(template_data)

        logger.info(f"Processed template {template_id} with signatures")
        return jsonify({"processed_data": processed_data}), 200
    except Exception as e:
        logger.error(f"Failed to process template {template_id} with signatures: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>/analyze", methods=["GET"], strict_slashes=False)
@roles_required("monteur", "verkoper", "administrator")
@rate_limit("60/minute")
def analyze_template(template_id):
    """
    Analyze a template to extract its structure and fields.

    Path Parameters:
        template_id (int): The ID of the template to analyze.

    Returns:
        JSON: Template analysis including fields, structure, and type.
    """
    try:
        analysis = document_template_service.analyze_template_structure(template_id)
        logger.info(f"Analyzed template {template_id}")
        return jsonify(analysis), 200
    except Exception as e:
        logger.error(f"Failed to analyze template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500
