import React, { useState, useEffect } from 'react';
import { getCustomerDeletionInfo } from '../services/customerService';

interface CustomerDeletionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  customerId: number;
  customerName: string;
}

interface DeletionInfo {
  customer_id: number;
  customer_name: string;
  customer_code: string;
  documents: {
    count: number;
    sub_documents: number;
    types: Record<string, number>;
    files_in_storage: number;
  };
  events: {
    count: number;
    pending: number;
    completed: number;
  };
  notes: {
    count: number;
  };
  quotations: {
    count: number;
    old_format: number;
    new_format: number;
    files_in_storage: number;
  };
  storage_impact: {
    total_files: number;
    warning: string;
  };
  total_items: number;
  warning_message: string;
}

const CustomerDeletionDialog: React.FC<CustomerDeletionDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  customerId,
  customerName
}) => {
  const [deletionInfo, setDeletionInfo] = useState<DeletionInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [confirmText, setConfirmText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const requiredConfirmText = `DELETE ${customerName}`;

  useEffect(() => {
    if (isOpen && customerId) {
      fetchDeletionInfo();
    }
  }, [isOpen, customerId]);

  const fetchDeletionInfo = async () => {
    setLoading(true);
    setError(null);
    try {
      const info = await getCustomerDeletionInfo(customerId);
      setDeletionInfo(info);
    } catch (err: any) {
      setError(err.message || 'Failed to load deletion information');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = async () => {
    if (confirmText !== requiredConfirmText) {
      return;
    }
    
    setIsDeleting(true);
    try {
      await onConfirm();
      onClose();
    } catch (err) {
      // Error handling is done in parent component
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setConfirmText('');
    setDeletionInfo(null);
    setError(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-red-600 flex items-center">
            <span className="text-3xl mr-2">⚠️</span>
            PERMANENT DELETION WARNING
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
            disabled={isDeleting}
          >
            <span className="text-2xl">&times;</span>
          </button>
        </div>

        {loading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading deletion information...</p>
          </div>
        )}

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <p className="font-bold">Error:</p>
            <p>{error}</p>
          </div>
        )}

        {deletionInfo && (
          <div className="space-y-6">
            <div className="bg-red-50 border-l-4 border-red-500 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <span className="text-red-500 text-xl">🚨</span>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-red-800">
                    You are about to permanently delete customer: {deletionInfo.customer_name}
                  </h3>
                  <p className="mt-2 text-red-700">
                    This action will permanently remove ALL associated data and CANNOT be undone.
                  </p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                  📄 Documents
                </h4>
                <p className="text-sm text-gray-600">
                  {deletionInfo.documents.count + deletionInfo.documents.sub_documents} total documents
                </p>
                <p className="text-sm text-gray-600">
                  {deletionInfo.documents.files_in_storage} files in storage
                </p>
                {Object.keys(deletionInfo.documents.types).length > 0 && (
                  <div className="mt-2">
                    <p className="text-xs text-gray-500">Types:</p>
                    {Object.entries(deletionInfo.documents.types).map(([type, count]) => (
                      <span key={type} className="inline-block bg-gray-200 text-xs px-2 py-1 rounded mr-1 mt-1">
                        {type}: {count}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                  📅 Events
                </h4>
                <p className="text-sm text-gray-600">{deletionInfo.events.count} total events</p>
                <p className="text-sm text-gray-600">
                  {deletionInfo.events.pending} pending, {deletionInfo.events.completed} completed
                </p>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                  📝 Notes
                </h4>
                <p className="text-sm text-gray-600">{deletionInfo.notes.count} customer notes</p>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                  💰 Quotations
                </h4>
                <p className="text-sm text-gray-600">{deletionInfo.quotations.count} total quotations</p>
                <p className="text-sm text-gray-600">
                  {deletionInfo.quotations.files_in_storage} PDF files in storage
                </p>
              </div>
            </div>

            <div className="bg-orange-50 border border-orange-200 p-4 rounded-lg">
              <h4 className="font-semibold text-orange-800 mb-2 flex items-center">
                🗄️ Storage Impact
              </h4>
              <p className="text-sm text-orange-700">
                {deletionInfo.storage_impact.total_files} files will be permanently deleted from Firebase Storage
              </p>
              <p className="text-xs text-orange-600 mt-1">
                {deletionInfo.storage_impact.warning}
              </p>
            </div>

            <div className="bg-red-100 border border-red-300 p-4 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">Total Impact</h4>
              <p className="text-red-700">
                <strong>{deletionInfo.total_items}</strong> database records will be permanently deleted
              </p>
              <p className="text-red-700">
                <strong>{deletionInfo.storage_impact.total_files}</strong> files will be permanently deleted from storage
              </p>
            </div>

            <div className="border-t pt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                To confirm deletion, type: <span className="font-mono bg-gray-100 px-2 py-1 rounded">{requiredConfirmText}</span>
              </label>
              <input
                type="text"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder={`Type "${requiredConfirmText}" to confirm`}
                disabled={isDeleting}
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                onClick={handleClose}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                disabled={isDeleting}
              >
                Cancel
              </button>
              <button
                onClick={handleConfirm}
                disabled={confirmText !== requiredConfirmText || isDeleting}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  confirmText === requiredConfirmText && !isDeleting
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {isDeleting ? (
                  <span className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Deleting...
                  </span>
                ) : (
                  'DELETE CUSTOMER PERMANENTLY'
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerDeletionDialog;
