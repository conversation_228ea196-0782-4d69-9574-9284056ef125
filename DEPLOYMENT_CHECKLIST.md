# Production Deployment Checklist for Render

## Pre-Deployment Preparation

### 1. Environment Variables Setup
**Backend (Render Web Service)**
- [ ] `SECRET_KEY` - Auto-generated by Render
- [ ] `DATABASE_URL` - Auto-set by PostgreSQL service
- [ ] `FIREBASE_CREDENTIALS_JSON` - Firebase service account JSON (as string)
- [ ] `FIREBASE_STORAGE_BUCKET` - Your Firebase storage bucket name
- [ ] `FRONTEND_URL` - Your frontend domain (e.g., https://yourapp.web.app)
- [ ] `FIREBASE_HOSTING_DOMAIN` - Firebase hosting domain if different
- [ ] `ADMIN_EMAIL` - Initial admin user email
- [ ] `REDIS_URL` - Auto-set by Redis service
- [ ] `FLASK_ENV=production`
- [ ] `FLASK_DEBUG=False`
- [ ] `RATE_LIMIT_ENABLED=True`
- [ ] `SANITIZE_RESPONSES=True`

**Frontend (Firebase Hosting or Render Static)**
- [ ] `VITE_API_URL` - Backend API URL (e.g., https://yourapi.onrender.com/api)
- [ ] `VITE_FIREBASE_API_KEY` - Firebase web API key
- [ ] `VITE_FIREBASE_AUTH_DOMAIN` - Firebase auth domain
- [ ] `VITE_FIREBASE_PROJECT_ID` - Firebase project ID
- [ ] `VITE_FIREBASE_STORAGE_BUCKET` - Firebase storage bucket
- [ ] `VITE_FIREBASE_MESSAGING_SENDER_ID` - Firebase messaging sender ID
- [ ] `VITE_FIREBASE_APP_ID` - Firebase app ID
- [ ] `VITE_FIREBASE_MEASUREMENT_ID` - Firebase measurement ID (optional)

### 2. Firebase Configuration
- [ ] Firebase project created and configured
- [ ] Firebase Authentication enabled (Email/Password)
- [ ] Firebase Storage configured with proper security rules
- [ ] Service account key generated and added to backend env vars
- [ ] Firebase Hosting configured (if using Firebase for frontend)

### 3. Database Setup
- [ ] PostgreSQL service created on Render
- [ ] Database connection string configured
- [ ] Database initialization script ready

## Deployment Steps

### Phase 1: Backend Deployment
1. [ ] Create PostgreSQL database service on Render
2. [ ] Create Redis cache service on Render (optional but recommended)
3. [ ] Create web service for backend API
4. [ ] Configure all environment variables
5. [ ] Deploy backend service
6. [ ] Run database initialization: `python init_production_db.py`
7. [ ] Verify health check endpoint: `/api/health`

### Phase 2: Frontend Deployment
**Option A: Firebase Hosting (Recommended)**
1. [ ] Install Firebase CLI: `npm install -g firebase-tools`
2. [ ] Login to Firebase: `firebase login`
3. [ ] Initialize Firebase hosting: `firebase init hosting`
4. [ ] Configure environment variables in `.env.production`
5. [ ] Build frontend: `npm run build`
6. [ ] Deploy: `firebase deploy --only hosting`

**Option B: Render Static Site**
1. [ ] Create static site service on Render
2. [ ] Configure build command and environment variables
3. [ ] Deploy static site

### Phase 3: Post-Deployment Configuration
1. [ ] Test authentication flow
2. [ ] Test file upload/download functionality
3. [ ] Verify CORS configuration
4. [ ] Test API endpoints
5. [ ] Check security headers
6. [ ] Monitor logs for errors
7. [ ] Set up monitoring and alerts

## Security Checklist

### Backend Security
- [ ] HTTPS enforced (handled by Render)
- [ ] CORS properly configured for production domains
- [ ] CSRF protection enabled
- [ ] Rate limiting active
- [ ] Security headers configured
- [ ] Response sanitization enabled
- [ ] Firebase credentials secured
- [ ] Database credentials secured
- [ ] Logging configured (no sensitive data in logs)

### Frontend Security
- [ ] HTTPS enforced
- [ ] Content Security Policy configured
- [ ] XSS protection headers
- [ ] Firebase configuration secured
- [ ] No sensitive data in client-side code
- [ ] Proper error handling (no sensitive info exposed)

### Database Security
- [ ] Database encryption at rest (handled by Render)
- [ ] Connection encryption (SSL)
- [ ] Proper user permissions
- [ ] Regular backups configured
- [ ] No sensitive data in plain text

## Performance Optimization

### Backend
- [ ] Redis cache configured
- [ ] Database queries optimized
- [ ] Proper indexing on database tables
- [ ] Gunicorn workers configured appropriately
- [ ] File upload size limits set

### Frontend
- [ ] Build optimization enabled
- [ ] Static assets cached
- [ ] Code splitting implemented
- [ ] Images optimized
- [ ] Bundle size analyzed

## Monitoring and Maintenance

### Logging
- [ ] Application logs configured
- [ ] Error tracking set up
- [ ] Performance monitoring enabled
- [ ] Database query monitoring

### Backups
- [ ] Database backup strategy implemented
- [ ] Firebase Storage backup plan
- [ ] Configuration backup (environment variables)

### Updates
- [ ] Dependency update strategy
- [ ] Security patch process
- [ ] Database migration process
- [ ] Rollback plan documented

## Testing Checklist

### Functional Testing
- [ ] User registration/login
- [ ] Password reset functionality
- [ ] Customer management (CRUD operations)
- [ ] Document upload/download
- [ ] Document template system
- [ ] Event management
- [ ] Time tracking
- [ ] Quotation system
- [ ] User permissions (if applicable)

### Security Testing
- [ ] Authentication bypass attempts
- [ ] Authorization checks
- [ ] SQL injection protection
- [ ] XSS protection
- [ ] CSRF protection
- [ ] Rate limiting effectiveness
- [ ] File upload security

### Performance Testing
- [ ] Load testing on API endpoints
- [ ] Database performance under load
- [ ] File upload/download performance
- [ ] Frontend loading times
- [ ] Mobile responsiveness

## Troubleshooting Common Issues

### CORS Errors
- Check FRONTEND_URL environment variable
- Verify allowed origins in backend configuration
- Ensure protocol (https/http) matches

### Authentication Issues
- Verify Firebase configuration on both frontend and backend
- Check Firebase service account permissions
- Validate JWT token handling

### Database Connection Issues
- Verify DATABASE_URL format
- Check PostgreSQL service status
- Validate database permissions

### File Upload Issues
- Check Firebase Storage configuration
- Verify service account permissions
- Test Firebase Storage rules

## Post-Deployment Actions
1. [ ] Change default admin password
2. [ ] Set up monitoring alerts
3. [ ] Document API endpoints
4. [ ] Create user documentation
5. [ ] Set up regular backup verification
6. [ ] Plan for scaling (if needed)
