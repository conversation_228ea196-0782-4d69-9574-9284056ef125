import React, { useState, useEffect } from 'react';
import { DocumentTemplate } from '../types/document_template';
import { Customer } from '../types/customer';
import { FaSave, FaDownload, FaSpinner } from 'react-icons/fa';
import TemplateService from '../services/templateService';
import SignaturePad from './SignaturePad';
import LoadingSpinner from './LoadingSpinner';
import { MobileCard, MobileFormGroup, MobileFormActions, MobileButtonGroup } from './common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

interface WorkbonTemplateFormProps {
  template: DocumentTemplate;
  customer?: Customer;
  onSave: (blob: Blob, fileName: string) => void;
  onCancel: () => void;
}

interface FormData {
  [key: string]: any;
}

const WorkbonTemplateForm: React.FC<WorkbonTemplateFormProps> = ({
  template,
  customer,
  onSave,
  onCancel
}) => {
  const { isMobile } = useMobile();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [templateContent, setTemplateContent] = useState<ArrayBuffer | null>(null);
  const [generating, setGenerating] = useState<boolean>(false);
  const [structure, setStructure] = useState<any>(null);
  const [formData, setFormData] = useState<FormData>({});
  const [activeTab, setActiveTab] = useState<string>('customer');

  useEffect(() => {
    loadTemplate();
  }, [template.id]);

  useEffect(() => {
    if (customer && structure) {
      populateCustomerData();
    }
  }, [customer, structure]);

  const loadTemplate = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load the template
      const content = await TemplateService.loadTemplate(template.id);
      setTemplateContent(content);

      // Analyze the template to extract structure
      const analysis = await TemplateService.analyzeTemplate(content);
      
      if (analysis.isWerkbonTemplate && analysis.structure) {
        setStructure(analysis.structure);
        initializeFormData(analysis.structure);
      } else {
        throw new Error('This is not a valid werkbon template');
      }
    } catch (err: any) {
      setError(`Failed to load template: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const initializeFormData = (templateStructure: any) => {
    const initialData: FormData = {};

    // Initialize all fields with empty values
    templateStructure.customerFields?.forEach((field: any) => {
      initialData[field.name] = '';
    });

    templateStructure.workInfo?.forEach((field: any) => {
      initialData[field.name] = '';
    });

    templateStructure.workDetails?.forEach((field: any) => {
      initialData[field.name] = '';
    });

    templateStructure.signatures?.forEach((field: any) => {
      initialData[field.name] = '';
    });

    // Set current date
    const today = new Date().toISOString().split('T')[0];
    initialData['datum'] = today;

    setFormData(initialData);
  };

  const populateCustomerData = () => {
    if (!customer || !structure) return;

    const updatedData = { ...formData };

    // Map customer data to form fields
    const customerMapping = {
      klant_naam: customer.name || '',
      klant_adres: `${customer.address || ''} ${customer.postal_code || ''} ${customer.city || ''}`.trim(),
      contactpersoon: customer.contact_person || '',
      telefoon: customer.phone || customer.mobile || ''
    };

    Object.entries(customerMapping).forEach(([key, value]) => {
      if (structure.customerFields?.find((f: any) => f.name === key && f.autoPopulate)) {
        updatedData[key] = value;
      }
    });

    setFormData(updatedData);
  };

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const createTemplateData = () => {
    const data = { ...formData };

    // Add current date if not set
    if (!data.datum) {
      data.datum = new Date().toLocaleDateString('nl-NL');
    }

    return data;
  };

  const handleSave = async () => {
    if (!templateContent) return;

    try {
      setGenerating(true);
      setError(null);

      // Create template data from form
      const data = createTemplateData();

      // Fill the template with the data
      const docxBlob = await TemplateService.fillTemplate(templateContent, data);

      // Generate a filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const customerName = customer?.name?.replace(/\s+/g, '_') || 'unknown';
      const fileName = `Werkbon_${customerName}_${timestamp}.docx`;

      // Pass the blob and filename to the parent component
      onSave(docxBlob, fileName);
    } catch (err: any) {
      setError(`Failed to generate document: ${err.message || 'Unknown error'}`);
    } finally {
      setGenerating(false);
    }
  };

  const renderField = (field: any) => {
    const value = formData[field.name] || '';

    if (field.type === 'signature') {
      return (
        <MobileFormGroup key={field.name} label={field.label}>
          <SignaturePad
            onChange={(signature) => handleFieldChange(field.name, signature)}
            value={value}
            disabled={generating}
          />
        </MobileFormGroup>
      );
    }

    if (field.type === 'textarea') {
      return (
        <MobileFormGroup key={field.name} label={field.label}>
          <textarea
            className="mobile-form-input"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            disabled={generating}
            placeholder={`Vul ${field.label.toLowerCase()} in`}
            rows={3}
          />
        </MobileFormGroup>
      );
    }

    return (
      <MobileFormGroup key={field.name} label={field.label}>
        <input
          type={field.type || 'text'}
          className="mobile-form-input"
          value={value}
          onChange={(e) => handleFieldChange(field.name, e.target.value)}
          disabled={generating}
          placeholder={`Vul ${field.label.toLowerCase()} in`}
        />
      </MobileFormGroup>
    );
  };

  if (loading) {
    return (
      <MobileCard>
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner />
          <span className="ml-2">Loading werkbon template...</span>
        </div>
      </MobileCard>
    );
  }

  if (error) {
    return (
      <MobileCard>
        <div className="text-center text-red-500">
          <p>{error}</p>
          <button onClick={onCancel} className="btn btn-outline btn-sm mt-4">
            Go Back
          </button>
        </div>
      </MobileCard>
    );
  }

  if (!structure) {
    return (
      <MobileCard>
        <div className="text-center text-red-500">
          <p>Failed to load werkbon template structure</p>
          <button onClick={onCancel} className="btn btn-outline btn-sm mt-4">
            Go Back
          </button>
        </div>
      </MobileCard>
    );
  }

  const tabs = [
    { id: 'customer', label: 'Klant Gegevens', icon: '👤' },
    { id: 'work', label: 'Monteur & Tijd', icon: '👷' },
    { id: 'details', label: 'Werkzaamheden', icon: '🔧' },
    { id: 'signatures', label: 'Handtekeningen', icon: '✍️' }
  ];

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg overflow-hidden">
      {/* Header */}
      <div className="bg-amspm-primary text-white p-4">
        <h3 className="text-lg font-semibold">Werkbon Template</h3>
        <p className="text-sm opacity-90">{template.name}</p>
        {customer && (
          <p className="text-sm opacity-75 mt-1">Voor: {customer.name}</p>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 p-4">
          <div className="text-red-700 dark:text-red-300">{error}</div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-dark-border">
        <nav className={`flex ${isMobile ? 'overflow-x-auto' : ''}`}>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`${isMobile ? 'min-w-0 flex-shrink-0' : 'flex-1'} px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-amspm-primary text-amspm-primary bg-blue-50 dark:bg-blue-900/20'
                  : 'border-transparent text-gray-500 dark:text-dark-text-light hover:text-gray-700 dark:hover:text-dark-text hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {/* Customer Information Tab */}
        {activeTab === 'customer' && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4">Klant Gegevens</h4>
            <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'} gap-4`}>
              {structure.customerFields?.map((field: any) => (
                <MobileFormGroup
                  key={field.name}
                  label={`${field.label}${field.autoPopulate && customer ? ' (auto)' : ''}`}
                >
                  <input
                    type={field.type || 'text'}
                    className="mobile-form-input"
                    value={formData[field.name] || ''}
                    onChange={(e) => handleFieldChange(field.name, e.target.value)}
                    disabled={generating}
                    placeholder={`Vul ${field.label.toLowerCase()} in`}
                  />
                </MobileFormGroup>
              ))}
            </div>
          </div>
        )}

        {/* Monteur & Tijd Tab */}
        {activeTab === 'work' && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4">Monteur & Tijd</h4>
            <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'} gap-4`}>
              {structure.workInfo?.map((field: any) => renderField(field))}
            </div>
          </div>
        )}

        {/* Work Details Tab */}
        {activeTab === 'details' && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4">Werkzaamheden</h4>
            <div className="space-y-4">
              {structure.workDetails?.map((field: any) => renderField(field))}
            </div>
          </div>
        )}

        {/* Signatures Tab */}
        {activeTab === 'signatures' && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4">Handtekeningen</h4>
            <div className="space-y-6">
              {structure.signatures?.map((field: any) => renderField(field))}
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <MobileFormActions>
        <MobileButtonGroup>
          <button
            onClick={onCancel}
            disabled={generating}
            className="btn btn-outline flex-1"
          >
            Annuleren
          </button>
          <button
            onClick={handleSave}
            disabled={generating || !templateContent}
            className="btn btn-primary flex-1"
          >
            {generating ? (
              <>
                <FaSpinner className="animate-spin mr-2" />
                Genereren...
              </>
            ) : (
              <>
                <FaSave className="mr-2" />
                Opslaan als DOCX
              </>
            )}
          </button>
        </MobileButtonGroup>
      </MobileFormActions>
    </div>
  );
};

export default WorkbonTemplateForm;
