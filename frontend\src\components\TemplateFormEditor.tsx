import React, { useState, useEffect } from 'react';
import { DocumentTemplate } from '../types/document_template';
import { Customer } from '../types/customer';
import { FaSave, FaDownload, FaSpinner } from 'react-icons/fa';
import TemplateService from '../services/templateService';
import InspectionTemplateForm from './InspectionTemplateForm';
import { MobileFormGroup, MobileFormActions, MobileButtonGroup } from './common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

interface TemplateFormEditorProps {
  template: DocumentTemplate;
  customer?: Customer;
  onSave: (blob: Blob, fileName: string) => void;
  onCancel: () => void;
}

interface FormField {
  name: string;
  label: string;
  value: string;
  type: 'text' | 'checkbox' | 'date' | 'textarea';
}

const TemplateFormEditor: React.FC<TemplateFormEditorProps> = ({
  template,
  customer,
  onSave,
  onCancel
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [fields, setFields] = useState<FormField[]>([]);
  const [templateContent, setTemplateContent] = useState<ArrayBuffer | null>(null);
  const [generating, setGenerating] = useState<boolean>(false);
  const [isInspectionTemplate, setIsInspectionTemplate] = useState<boolean>(false);
  const { isMobile } = useMobile();

  useEffect(() => {
    loadTemplate();
  }, [template.id]);

  const loadTemplate = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load the template
      const content = await TemplateService.loadTemplate(template.id);
      setTemplateContent(content);

      // Analyze the template to extract fields
      const analysis = await TemplateService.analyzeTemplate(content);

      // Check if this is an inspection template
      if (analysis.isInspectionTemplate) {
        setIsInspectionTemplate(true);
        return; // InspectionTemplateForm will handle this
      }

      // Create form fields for regular templates
      const formFields: FormField[] = [
        ...analysis.fields.map(field => ({
          name: field.name,
          label: field.label,
          value: '',
          type: field.name.includes('datum') ? 'date' as const : 'text' as const
        })),
        ...analysis.checkboxes.map(field => ({
          name: field.name,
          label: field.label,
          value: 'false',
          type: 'checkbox' as const
        }))
      ];

      setFields(formFields);
    } catch (err: any) {
      setError(`Failed to load template: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (index: number, value: string) => {
    const updatedFields = [...fields];
    updatedFields[index].value = value;
    setFields(updatedFields);
  };

  const handleCheckboxChange = (index: number, checked: boolean) => {
    const updatedFields = [...fields];
    updatedFields[index].value = checked ? 'true' : 'false';
    setFields(updatedFields);
  };

  const createTemplateData = () => {
    const data: Record<string, any> = {};

    fields.forEach(field => {
      if (field.type === 'checkbox') {
        // For checkboxes, we'll use true/false values
        data[field.name] = field.value === 'true';
      } else {
        data[field.name] = field.value;
      }
    });

    return data;
  };

  const handleSave = async () => {
    if (!templateContent) return;

    try {
      setGenerating(true);
      setError(null);

      // Create template data from form fields
      const data = createTemplateData();

      // Fill the template with the data
      const docxBlob = await TemplateService.fillTemplate(templateContent, data);

      // Generate a filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${template.name.replace(/\s+/g, '_')}_${timestamp}.docx`;

      // Pass the blob and filename to the parent component
      onSave(docxBlob, fileName);
    } catch (err: any) {
      setError(`Failed to generate document: ${err.message || 'Unknown error'}`);
    } finally {
      setGenerating(false);
    }
  };

  const handleDownload = async () => {
    if (!templateContent) return;

    try {
      setGenerating(true);
      setError(null);

      // Create template data from form fields
      const data = createTemplateData();

      // Fill the template with the data
      const docxBlob = await TemplateService.fillTemplate(templateContent, data);

      // Generate a filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${template.name.replace(/\s+/g, '_')}_${timestamp}.docx`;

      // Download the document
      TemplateService.downloadDocument(docxBlob, fileName);
    } catch (err: any) {
      setError(`Failed to download document: ${err.message || 'Unknown error'}`);
    } finally {
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin text-amspm-primary text-2xl" />
        <span className="ml-2">Loading template...</span>
      </div>
    );
  }

  // Use InspectionTemplateForm for inspection templates
  if (isInspectionTemplate) {
    return (
      <InspectionTemplateForm
        template={template}
        customer={customer}
        onSave={onSave}
        onCancel={onCancel}
      />
    );
  }

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-4">
      <div className="mobile-header">
        <div className="flex-1">
          <h3 className="mobile-header-title">{template.name}</h3>
        </div>
        {!isMobile && (
          <div className="mobile-header-actions">
            <button
              onClick={onCancel}
              className="btn btn-outline btn-sm mobile-touch-target"
              disabled={generating}
            >
              Cancel
            </button>
            <button
              onClick={handleDownload}
              className="btn btn-outline btn-sm mobile-touch-target"
              disabled={generating}
              title="Download Document"
            >
              <FaDownload className="mr-1" /> Download
            </button>
            <button
              onClick={handleSave}
              className="btn btn-primary btn-sm mobile-touch-target"
              disabled={generating}
            >
              {generating ? (
                <><FaSpinner className="animate-spin mr-1" /> Saving</>
              ) : (
                <><FaSave className="mr-1" /> Save</>
              )}
            </button>
          </div>
        )}
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 p-3 rounded-lg mb-4">
          {error}
        </div>
      )}

      <div className="border border-gray-200 dark:border-gray-700 rounded-md p-4 mb-4 bg-white dark:bg-dark-input overflow-y-auto mobile-scroll-container">
        {fields.length === 0 ? (
          <p className="text-gray-500 dark:text-dark-text-light italic">
            No form fields found in this template. Make sure your template contains fields in the format {'{field_name}'}.
          </p>
        ) : (
          <div className="space-y-4">
            {fields.map((field, index) => (
              <MobileFormGroup
                key={index}
                label={field.label}
                className="form-control"
              >

                {field.type === 'checkbox' ? (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox mobile-touch-target"
                      checked={field.value === 'true'}
                      onChange={(e) => handleCheckboxChange(index, e.target.checked)}
                      disabled={generating}
                    />
                    <span className="ml-2 text-sm text-amspm-text dark:text-dark-text">
                      {field.value === 'true' ? 'Ja' : 'Nee'}
                    </span>
                  </div>
                ) : field.type === 'date' ? (
                  <input
                    type="date"
                    className="mobile-form-input"
                    value={field.value}
                    onChange={(e) => handleFieldChange(index, e.target.value)}
                    disabled={generating}
                  />
                ) : field.type === 'textarea' ? (
                  <textarea
                    className="mobile-form-input resize-none"
                    value={field.value}
                    onChange={(e) => handleFieldChange(index, e.target.value)}
                    disabled={generating}
                    placeholder={`Vul ${field.label.toLowerCase()} in`}
                    rows={3}
                  />
                ) : (
                  <input
                    type="text"
                    className="mobile-form-input"
                    value={field.value}
                    onChange={(e) => handleFieldChange(index, e.target.value)}
                    disabled={generating}
                    placeholder={`Vul ${field.label.toLowerCase()} in`}
                  />
                )}
              </MobileFormGroup>
            ))}
          </div>
        )}
      </div>

      <div className="text-sm text-gray-500 dark:text-dark-text-light mb-4">
        <p>Fill in the form fields and click Save when you're done.</p>
      </div>

      {/* Mobile action buttons */}
      {isMobile && (
        <MobileFormActions>
          <MobileButtonGroup direction="vertical">
            <button
              onClick={handleSave}
              className="btn btn-primary mobile-touch-target"
              disabled={generating}
            >
              {generating ? (
                <><FaSpinner className="animate-spin mr-2" /> Saving</>
              ) : (
                <><FaSave className="mr-2" /> Save</>
              )}
            </button>
            <button
              onClick={handleDownload}
              className="btn btn-outline mobile-touch-target"
              disabled={generating}
            >
              <FaDownload className="mr-2" /> Download
            </button>
            <button
              onClick={onCancel}
              className="btn btn-outline mobile-touch-target"
              disabled={generating}
            >
              Cancel
            </button>
          </MobileButtonGroup>
        </MobileFormActions>
      )}
    </div>
  );
};

export default TemplateFormEditor;
