"""
Image utility functions for handling signature images and other image processing tasks.
"""
import base64
import io
import logging
from PIL import Image
from typing import Tuple, Optional

logger = logging.getLogger(__name__)

def process_signature_base64(base64_data: str) -> Tuple[bytes, str]:
    """
    Process a base64 signature image and return optimized image bytes.
    
    Args:
        base64_data: Base64 encoded image data (with or without data URL prefix)
        
    Returns:
        tuple: (image_bytes, content_type)
    """
    try:
        # Remove data URL prefix if present
        if base64_data.startswith('data:image/'):
            base64_data = base64_data.split(',')[1]
        
        # Decode base64 data
        image_data = base64.b64decode(base64_data)
        
        # Open image with PIL
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary (removes alpha channel)
        if image.mode in ('RGBA', 'LA'):
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'RGBA':
                background.paste(image, mask=image.split()[-1])
            else:
                background.paste(image, mask=image.split()[-1])
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Optimize signature image
        # Resize if too large (max 400x200 pixels)
        max_width, max_height = 400, 200
        if image.width > max_width or image.height > max_height:
            image.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
        
        # Save as PNG for better quality with transparency support
        output = io.BytesIO()
        image.save(output, format='PNG', optimize=True)
        image_bytes = output.getvalue()
        
        logger.info(f"Processed signature image: {len(image_bytes)} bytes, {image.width}x{image.height}")
        
        return image_bytes, 'image/png'
        
    except Exception as e:
        logger.error(f"Failed to process signature image: {str(e)}")
        raise ValueError(f"Invalid signature image data: {str(e)}")

def validate_signature_image(base64_data: str) -> bool:
    """
    Validate that the base64 data represents a valid signature image.
    
    Args:
        base64_data: Base64 encoded image data
        
    Returns:
        bool: True if valid, False otherwise
    """
    try:
        if not base64_data or not isinstance(base64_data, str):
            return False
            
        # Check if it's a data URL
        if base64_data.startswith('data:image/'):
            # Extract the base64 part
            if ',' not in base64_data:
                return False
            base64_part = base64_data.split(',')[1]
        else:
            base64_part = base64_data
        
        # Try to decode base64
        try:
            image_data = base64.b64decode(base64_part)
        except Exception:
            return False
        
        # Try to open as image
        try:
            image = Image.open(io.BytesIO(image_data))
            # Check if image has reasonable dimensions
            if image.width < 10 or image.height < 10:
                return False
            if image.width > 2000 or image.height > 2000:
                return False
            return True
        except Exception:
            return False
            
    except Exception as e:
        logger.warning(f"Signature validation failed: {str(e)}")
        return False

def create_signature_placeholder(width: int = 200, height: int = 100) -> bytes:
    """
    Create a placeholder signature image.
    
    Args:
        width: Image width in pixels
        height: Image height in pixels
        
    Returns:
        bytes: PNG image data
    """
    try:
        # Create a white background image
        image = Image.new('RGB', (width, height), color='white')
        
        # Save as PNG
        output = io.BytesIO()
        image.save(output, format='PNG')
        return output.getvalue()
        
    except Exception as e:
        logger.error(f"Failed to create signature placeholder: {str(e)}")
        raise ValueError(f"Failed to create placeholder: {str(e)}")
