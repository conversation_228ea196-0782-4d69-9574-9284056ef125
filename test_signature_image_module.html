<!DOCTYPE html>
<html>
<head>
    <title>Test Signature Image Module</title>
    <script src="https://unpkg.com/pizzip@3.1.8/dist/pizzip.js"></script>
    <script src="https://unpkg.com/docxtemplater@3.61.1/build/docxtemplater.js"></script>
    <script src="https://unpkg.com/docxtemplater-image-module@3.1.0/build/index.js"></script>
</head>
<body>
    <h1>Test Signature Image Module</h1>
    <canvas id="testCanvas" width="400" height="200" style="border: 1px solid black;"></canvas>
    <br><br>
    <button onclick="createTestSignature()">Create Test Signature</button>
    <button onclick="testImageModule()">Test Image Module</button>
    <br><br>
    <div id="output"></div>

    <script>
        let testSignatureData = null;

        function createTestSignature() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw a simple signature
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(50, 100);
            ctx.lineTo(150, 80);
            ctx.lineTo(250, 120);
            ctx.lineTo(350, 100);
            ctx.stroke();
            
            // Convert to base64
            testSignatureData = canvas.toDataURL('image/png');
            document.getElementById('output').innerHTML = `<p>Test signature created: ${testSignatureData.length} characters</p>`;
        }

        function testImageModule() {
            if (!testSignatureData) {
                alert('Please create a test signature first');
                return;
            }

            try {
                // Test the image processing function
                const imageModule = new DocxtemplaterImageModule({
                    centered: false,
                    getImage: (tagValue, tagName) => {
                        console.log(`Processing image for tag: ${tagName}`, tagValue ? `has data (${tagValue.length} chars)` : 'no data');

                        if (tagName && tagName.includes('handtekening') && tagValue) {
                            if (typeof tagValue === 'string' && tagValue.startsWith('data:image/')) {
                                try {
                                    const base64Data = tagValue.split(',')[1];
                                    if (!base64Data) {
                                        console.error('No base64 data found in signature');
                                        return null;
                                    }

                                    const binaryString = atob(base64Data);
                                    const bytes = new Uint8Array(binaryString.length);
                                    for (let i = 0; i < binaryString.length; i++) {
                                        bytes[i] = binaryString.charCodeAt(i);
                                    }
                                    
                                    console.log(`Successfully processed signature image: ${bytes.length} bytes for ${tagName}`);
                                    return bytes;
                                } catch (error) {
                                    console.error(`Error processing signature image for ${tagName}:`, error);
                                    return null;
                                }
                            }
                        }
                        
                        return null;
                    },
                    getSize: (img, tagValue, tagName) => {
                        if (tagName && tagName.includes('handtekening')) {
                            return [2286000, 1097280]; // 2.5" x 1.2"
                        }
                        return [1828800, 914400]; // Default size
                    }
                });

                // Test the getImage function directly
                const result = imageModule.getImage(testSignatureData, 'klant_handtekening');
                
                if (result) {
                    document.getElementById('output').innerHTML += `<p style="color: green;">✅ Image module test PASSED: Processed ${result.length} bytes</p>`;
                } else {
                    document.getElementById('output').innerHTML += `<p style="color: red;">❌ Image module test FAILED: getImage returned null</p>`;
                }

            } catch (error) {
                document.getElementById('output').innerHTML += `<p style="color: red;">❌ Image module test ERROR: ${error.message}</p>`;
                console.error('Image module test error:', error);
            }
        }
    </script>
</body>
</html>
