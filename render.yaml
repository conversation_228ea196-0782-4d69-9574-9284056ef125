services:
  # Backend API Service
  - type: web
    name: customer-management-api
    env: python
    region: oregon
    plan: starter
    rootDir: backend
    buildCommand: |
      apt-get update &&
      apt-get install -y libmagic1 &&
      pip install --upgrade pip &&
      pip install -r requirements.txt &&
      chmod +x start.sh
    startCommand: ./start.sh
    healthCheckPath: /api/health
    envVars:
      - key: FLASK_ENV
        value: production
      - key: FLASK_DEBUG
        value: False
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: PORT
        fromService:
          type: web
          name: customer-management-api
          property: port
      # Database URL will be set from Render PostgreSQL service
      - key: DATABASE_URL
        fromDatabase:
          name: customer-management-db
          property: connectionString
      # Security keys - set these in Render dashboard
      - key: SECRET_KEY
        generateValue: true
      # Firebase configuration - set these in Render dashboard
      - key: FIREBASE_CREDENTIALS_PATH
        value: /etc/secrets/firebase-service-account-key.json
      - key: FIREBASE_STORAGE_BUCKET
        sync: false  # Set manually in dashboard
      # Frontend URL for CORS
      - key: FRONTEND_URL
        sync: false  # Set manually in dashboard
      - key: FIREBASE_HOSTING_DOMAIN
        sync: false  # Set manually in dashboard
      # Admin user
      - key: ADMIN_EMAIL
        sync: false  # Set manually in dashboard
      # Logging
      - key: LOG_FILE
        value: /tmp/app.log
      # Cache configuration - use SimpleCache (no Redis needed)
      - key: CACHE_TYPE
        value: SimpleCache
      - key: CACHE_TIMEOUT
        value: 300
      - key: CACHE_KEY_PREFIX
        value: customer_mgmt_prod_
      # Rate limiting
      - key: RATE_LIMIT_ENABLED
        value: True
      # Security settings
      - key: SANITIZE_RESPONSES
        value: True

  # PostgreSQL Database
  - type: pserv
    name: customer-management-db
    env: postgresql
    plan: starter  # Change to standard or pro for production
    databaseName: customer_management
    databaseUser: customer_mgmt_user

  # Redis Cache removed - using SimpleCache instead to save costs

# Static site for frontend (if using Render for frontend hosting)
# Alternatively, you can use Firebase Hosting
  - type: static
    name: customer-management-frontend
    buildCommand: |
      cd frontend &&
      npm ci &&
      npm run build
    staticPublishPath: frontend/dist
    envVars:
      - key: NODE_ENV
        value: production
      # API URL pointing to the backend service
      - key: VITE_API_URL
        fromService:
          type: web
          name: customer-management-api
          property: host
      # Firebase configuration - set these in Render dashboard
      - key: VITE_FIREBASE_API_KEY
        sync: false
      - key: VITE_FIREBASE_AUTH_DOMAIN
        sync: false
      - key: VITE_FIREBASE_PROJECT_ID
        sync: false
      - key: VITE_FIREBASE_STORAGE_BUCKET
        sync: false
      - key: VITE_FIREBASE_MESSAGING_SENDER_ID
        sync: false
      - key: VITE_FIREBASE_APP_ID
        sync: false
      - key: VITE_FIREBASE_MEASUREMENT_ID
        sync: false
    headers:
      - path: /*
        name: X-Frame-Options
        value: DENY
      - path: /*
        name: X-Content-Type-Options
        value: nosniff
      - path: /*
        name: X-XSS-Protection
        value: 1; mode=block
      - path: /*
        name: Strict-Transport-Security
        value: max-age=31536000; includeSubDomains
      - path: /*
        name: Content-Security-Policy
        value: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://identitytoolkit.googleapis.com https://securetoken.googleapis.com; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' data: https://fonts.gstatic.com; frame-ancestors 'none';
    routes:
      - type: rewrite
        source: /*
        destination: /index.html
