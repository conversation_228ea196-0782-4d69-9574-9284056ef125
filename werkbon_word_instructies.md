# Werkbon Word Template - Stap voor Stap Instructies

## Stap 1: Nieuw Word Document
1. Open Microsoft Word
2. Maak een nieuw leeg document
3. <PERSON><PERSON> marges in op 2 cm rondom (Layout → Marges → Aangepast)

## Stap 2: Header maken
1. Typ: **W E R K B R I E F J E**
2. Selecteer de tekst en:
   - Lettergrootte: 18pt
   - Vetgedrukt
   - Gecentreerd
   - Lettertype: Arial of Calibri
3. Druk 2x Enter voor ruimte

## Stap 3: Klantgegevens Sectie
Maak een tabel van 2 kolommen en 5 rijen:

1. Ga naar Invoegen → Tabel → 2x5 tabel
2. Vul de tabel als volgt in:

| Linker kolom | Rechter kolom |
|--------------|---------------|
| Weeknummer: {weeknummer} | Naam: {naam} |
| Opdrachtgever: {opdrachtgever} | Adres: {adres} |
| Werklocatie: {werklocatie} | Postcode/plaats: {postcode_plaats} |
| Postcode/plaats: {postcode_plaats} | Geb.datum: {geb_datum} |
| Afdeling: {afdeling} | Personeelsnr.: {personeelsnr} |

3. Verwijder alle tabelranden (Tabelontwerp → Randen → Geen rand)
4. Druk Enter voor ruimte

## Stap 4: Werkzaamheden Sectie
1. Typ: **WERKZAAMHEDEN** (vetgedrukt)
2. Druk Enter
3. Typ: **Soort werkzaamheden:**
4. Druk Enter en typ: {soort_werkzaamheden}
5. Druk 2x Enter
6. Typ: **Gebruikte componenten:**
7. Druk Enter en typ: {gebruikte_componenten}
8. Druk 2x Enter
9. Typ: **Bijzonderheden:**
10. Druk Enter en typ: {bijzonderheden}
11. Druk 2x Enter

## Stap 5: Tijdregistratie Tabel
1. Typ: **TIJDREGISTRATIE** (vetgedrukt)
2. Druk Enter
3. Maak een tabel van 8 kolommen en 7 rijen (Invoegen → Tabel → 8x7)
4. Vul de header rij in:

| Datum | van-tot | van-tot | gewone uren | toeslag ...% | toeslag ...% | toeslag ...% | Totaal uren |

5. Vul de data rijen in:

**Rij 2:**
| {datum_1} | {van_tot_1_start} - {van_tot_1_end} | | {gewone_uren_1} | {toeslag_1_perc}% - {toeslag_1_uren} | {toeslag_2_perc}% - {toeslag_2_uren} | {toeslag_3_perc}% - {toeslag_3_uren} | {totaal_uren_1} |

**Rij 3:**
| {datum_2} | {van_tot_2_start} - {van_tot_2_end} | | {gewone_uren_2} | | | | {totaal_uren_2} |

**Rijen 4-6:** Laat leeg voor handmatige invulling

**Rij 7 (Totaal):**
| Totaal: | | | {totaal_gewone_uren} | {totaal_toeslag_uren} | | | {totaal_alle_uren} |

6. Formateer de tabel:
   - Selecteer hele tabel
   - Tabelontwerp → Randen → Alle randen
   - Header rij vetgedrukt maken
   - Totaal rij vetgedrukt maken

## Stap 6: Verklaring
1. Druk 2x Enter na de tabel
2. Typ: "Wij verklaren ons akkoord met het hierboven vermelde totaal aantal uren."
3. Druk 2x Enter

## Stap 7: Handtekeningen Sectie
1. Typ: **HANDTEKENINGEN** (vetgedrukt)
2. Druk Enter
3. Maak een tabel van 2 kolommen en 6 rijen
4. Vul in:

**Linker kolom:**
- Handtekening, naam en firmanaam:
- {opdrachtgever_handtekening}
- (lege rij voor ruimte)
- Naam: {opdrachtgever_naam}
- Firma: {opdrachtgever_firma}
- Datum: {datum_handtekening}

**Rechter kolom:**
- Handtekening uitzendkracht:
- {uitzendkracht_handtekening}
- (lege rijen)

5. Verwijder tabelranden
6. Maak de handtekening rijen hoger (ongeveer 2 cm) voor handtekening ruimte

## Stap 8: Opslaan
1. Ga naar Bestand → Opslaan als
2. Kies locatie
3. Bestandsnaam: **werkbon_template**
4. Bestandstype: **Word-document (*.docx)**
5. Klik Opslaan

## Stap 9: Uploaden naar AMSPM
1. Log in op AMSPM systeem
2. Ga naar Document Templates
3. Klik "Nieuwe Template"
4. Vul in:
   - Naam: "Werkbon Template"
   - Document Type: "werkbon"
   - Beschrijving: "Template voor werkbriefjes met tijdregistratie"
5. Upload het werkbon_template.docx bestand
6. Klik Opslaan

## Belangrijke Opmerkingen
- Gebruik exact deze placeholder namen: {weeknummer}, {opdrachtgever}, etc.
- Zorg dat alle placeholders tussen accolades staan: { }
- De handtekening placeholders {opdrachtgever_handtekening} en {uitzendkracht_handtekening} worden vervangen door digitale handtekeningen
- Het systeem herkent automatisch werkbon templates op basis van woorden zoals "Weeknummer", "Opdrachtgever", "gewone uren"

## Test de Template
Na uploaden kun je de template testen door:
1. Een klant te selecteren
2. Document type "werkbon" te kiezen
3. De template te selecteren
4. Te controleren of alle velden correct worden ingevuld
