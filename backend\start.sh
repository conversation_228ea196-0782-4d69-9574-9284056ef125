#!/bin/bash
set -e

echo "=== Starting Customer Management API ==="

# Set environment variables
export FLASK_ENV=production
export FLASK_DEBUG=False

# Debug information
echo "Current directory: $(pwd)"
echo "Python version: $(python --version)"
echo "Files in current directory:"
ls -la

# Test if we can import the WSGI app
echo "Testing WSGI app import..."
python -c "
try:
    import wsgi
    print('✓ Successfully imported wsgi module')
    print('✓ wsgi.application exists:', hasattr(wsgi, 'application'))
    if hasattr(wsgi, 'application'):
        print('✓ wsgi.application type:', type(wsgi.application))
        # Test if we can get basic info from the app
        try:
            with wsgi.application.app_context():
                print('✓ App context works')
                firebase_init = wsgi.application.config.get('FIREBASE_INITIALIZED', False)
                print('✓ Firebase initialized:', firebase_init)
        except Exception as ctx_e:
            print('⚠ App context issue:', ctx_e)
    else:
        print('✗ wsgi.application attribute not found')
        print('Available attributes:', [attr for attr in dir(wsgi) if not attr.startswith('_')])
except Exception as e:
    print('✗ Failed to import wsgi:', e)
    import traceback
    traceback.print_exc()
    exit(1)
"

# Check critical environment variables
echo "Checking environment variables..."
if [ -z "$SECRET_KEY" ]; then
    echo "⚠ WARNING: SECRET_KEY not set"
fi
if [ -z "$DATABASE_URL" ]; then
    echo "⚠ WARNING: DATABASE_URL not set"
fi
if [ -z "$FIREBASE_CREDENTIALS_JSON" ]; then
    echo "⚠ WARNING: FIREBASE_CREDENTIALS_JSON not set - Firebase features will be disabled"
fi
if [ -z "$FIREBASE_STORAGE_BUCKET" ]; then
    echo "⚠ WARNING: FIREBASE_STORAGE_BUCKET not set - Firebase storage will be disabled"
fi

# Initialize database if environment variables are available
if [ -n "$DATABASE_URL" ] && [ -n "$SECRET_KEY" ]; then
    echo "Initializing production database..."
    python init_production_db.py || echo "Database initialization failed, continuing..."
else
    echo "Skipping database initialization (missing DATABASE_URL or SECRET_KEY)"
fi

# Start the application
echo "Starting Flask application with Gunicorn..."
echo "Using WSGI entry point: wsgi:application"
exec gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 120 --access-logfile - --error-logfile - wsgi:application
