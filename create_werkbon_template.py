#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a professional werkbon template DOCX file for AMSPM Security
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn

def add_page_break(doc):
    """Add a page break to the document"""
    doc.add_page_break()

def set_font(run, font_name="Arial", font_size=11, bold=False):
    """Set font properties for a run"""
    run.font.name = font_name
    run.font.size = Pt(font_size)
    run.font.bold = bold

def create_werkbon_template():
    """Create the werkbon template DOCX file"""
    
    # Create new document
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.8)
        section.bottom_margin = Inches(0.8)
        section.left_margin = Inches(0.8)
        section.right_margin = Inches(0.8)
    
    # Header
    header = doc.add_heading('', level=1)
    header_run = header.runs[0] if header.runs else header.add_run()
    header_run.text = 'WERKBON AMSPM SECURITY'
    set_font(header_run, font_size=18, bold=True)
    header.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add some space
    doc.add_paragraph()
    
    # KLANTGEGEVENS Section
    klant_heading = doc.add_heading('KLANTGEGEVENS', level=2)
    set_font(klant_heading.runs[0], font_size=12, bold=True)
    
    # Create table for customer info
    klant_table = doc.add_table(rows=4, cols=2)
    klant_table.style = 'Table Grid'
    
    # Customer info rows
    customer_data = [
        ('Klant:', '{klant_naam}'),
        ('Adres:', '{klant_adres}'),
        ('Contactpersoon:', '{contactpersoon}'),
        ('Telefoon:', '{telefoon}')
    ]
    
    for i, (label, placeholder) in enumerate(customer_data):
        row = klant_table.rows[i]
        row.cells[0].text = label
        row.cells[1].text = placeholder
        
        # Format cells
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    set_font(run, font_size=11)
    
    # Add space
    doc.add_paragraph()
    
    # MONTEUR Section
    monteur_heading = doc.add_heading('MONTEUR', level=2)
    set_font(monteur_heading.runs[0], font_size=12, bold=True)
    
    # Create table for monteur info
    monteur_table = doc.add_table(rows=3, cols=2)
    monteur_table.style = 'Table Grid'
    
    monteur_data = [
        ('Naam:', '{monteur_naam}'),
        ('Datum:', '{datum}'),
        ('Tijd:', 'van {start_tijd} tot {eind_tijd}')
    ]
    
    for i, (label, placeholder) in enumerate(monteur_data):
        row = monteur_table.rows[i]
        row.cells[0].text = label
        row.cells[1].text = placeholder
        
        # Format cells
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    set_font(run, font_size=11)
    
    # Add space
    doc.add_paragraph()
    
    # UITGEVOERDE WERKZAAMHEDEN Section
    werk_heading = doc.add_heading('UITGEVOERDE WERKZAAMHEDEN', level=2)
    set_font(werk_heading.runs[0], font_size=12, bold=True)
    
    # Add text area for werkzaamheden
    werk_para = doc.add_paragraph('{werkzaamheden}')
    set_font(werk_para.runs[0], font_size=11)
    
    # Add some space for writing
    for _ in range(3):
        doc.add_paragraph('_' * 80)
    
    # Add space
    doc.add_paragraph()
    
    # GEBRUIKTE MATERIALEN Section
    materialen_heading = doc.add_heading('GEBRUIKTE MATERIALEN', level=2)
    set_font(materialen_heading.runs[0], font_size=12, bold=True)
    
    # Add text area for materialen
    materialen_para = doc.add_paragraph('{materialen}')
    set_font(materialen_para.runs[0], font_size=11)
    
    # Add some space for writing
    for _ in range(3):
        doc.add_paragraph('_' * 80)
    
    # Add space
    doc.add_paragraph()
    
    # BEVINDINGEN/OPMERKINGEN Section
    opmerkingen_heading = doc.add_heading('BEVINDINGEN/OPMERKINGEN', level=2)
    set_font(opmerkingen_heading.runs[0], font_size=12, bold=True)
    
    # Add text area for opmerkingen
    opmerkingen_para = doc.add_paragraph('{opmerkingen}')
    set_font(opmerkingen_para.runs[0], font_size=11)
    
    # Add some space for writing
    for _ in range(3):
        doc.add_paragraph('_' * 80)
    
    # Add space
    doc.add_paragraph()
    
    # AKKOORD Section
    akkoord_heading = doc.add_heading('AKKOORD', level=2)
    set_font(akkoord_heading.runs[0], font_size=12, bold=True)
    
    # Add akkoord text
    akkoord_text = doc.add_paragraph('Klant akkoord met uitgevoerde werkzaamheden:')
    set_font(akkoord_text.runs[0], font_size=11)
    
    # Add space
    doc.add_paragraph()
    
    # Create signature table
    sig_table = doc.add_table(rows=4, cols=2)
    sig_table.style = 'Table Grid'
    
    # Signature headers
    sig_table.cell(0, 0).text = 'Handtekening klant:'
    sig_table.cell(0, 1).text = 'Handtekening monteur:'
    
    # Signature placeholders (make these cells taller)
    sig_table.cell(1, 0).text = '{klant_handtekening}'
    sig_table.cell(1, 1).text = '{monteur_handtekening}'
    
    # Set signature cell height
    for i in range(2):
        row = sig_table.rows[1]
        row.height = Inches(1.0)
    
    # Names
    sig_table.cell(2, 0).text = 'Naam: {klant_naam}'
    sig_table.cell(2, 1).text = 'Naam: {monteur_naam}'
    
    # Date
    sig_table.cell(3, 0).text = 'Datum: {datum}'
    sig_table.cell(3, 1).text = ''
    
    # Format signature table
    for row in sig_table.rows:
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    set_font(run, font_size=11)
    
    # Save the document
    filename = 'werkbon_template.docx'
    doc.save(filename)
    print(f"✅ Werkbon template saved as: {filename}")
    print("📋 Upload this file to AMSPM with document type 'werkbon'")
    
    return filename

if __name__ == "__main__":
    try:
        # Check if python-docx is installed
        import docx
        print("🚀 Creating werkbon template...")
        create_werkbon_template()
        print("✨ Done! Template is ready to use.")
    except ImportError:
        print("❌ Error: python-docx not installed")
        print("💡 Install with: pip install python-docx")
        print("   Then run this script again")
