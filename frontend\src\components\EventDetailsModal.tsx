import React from 'react';
import { Event } from '../types/event';
import { 
  FaCalendarAlt, 
  FaUser, 
  FaUsers, 
  FaBuilding, 
  FaClock, 
  FaCheckCircle, 
  FaEdit, 
  FaTimes,
  FaMapMarkerAlt,
  FaFileAlt,
  FaExclamationTriangle
} from 'react-icons/fa';

interface EventDetailsModalProps {
  event: Event;
  onClose: () => void;
  onEdit?: () => void;
  onComplete?: () => void;
  canEdit?: boolean;
  canComplete?: boolean;
}

const EventDetailsModal: React.FC<EventDetailsModalProps> = ({
  event,
  onClose,
  onEdit,
  onComplete,
  canEdit = false,
  canComplete = false
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('nl-NL', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-orange-600 bg-orange-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const isOverdue = event.status === 'pending' && new Date(event.scheduled_date) < new Date();

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 p-4 flex items-center justify-center z-50"
      onClick={onClose}
    >
      <div
        className="bg-white dark:bg-dark-secondary shadow-xl rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex justify-between items-start p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <FaCalendarAlt className="text-amspm-primary" size={24} />
              <h2 className="text-2xl font-bold text-amspm-text">Event Details</h2>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(event.status)}`}>
                {event.status === 'completed' ? 'Completed' : 'Pending'}
              </span>
              {isOverdue && (
                <span className="px-3 py-1 rounded-full text-sm font-medium text-red-600 bg-red-100 flex items-center">
                  <FaExclamationTriangle className="mr-1" size={12} />
                  Overdue
                </span>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Event Type & Description */}
          <div>
            <h3 className="text-lg font-semibold text-amspm-text mb-3">Event Information</h3>
            <div className="bg-gray-50 dark:bg-dark-card rounded-lg p-4 space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Event Type</label>
                <p className="text-lg font-medium text-amspm-text capitalize">
                  {event.event_type.replace(/_/g, ' ')}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Description</label>
                <p className="text-amspm-text">{event.description}</p>
              </div>
            </div>
          </div>

          {/* Schedule */}
          <div>
            <h3 className="text-lg font-semibold text-amspm-text mb-3 flex items-center">
              <FaClock className="mr-2" />
              Schedule
            </h3>
            <div className="bg-gray-50 dark:bg-dark-card rounded-lg p-4">
              <p className="text-amspm-text font-medium">{formatDate(event.scheduled_date)}</p>
              {isOverdue && (
                <p className="text-red-600 text-sm mt-1">
                  This event is overdue by {Math.ceil((new Date().getTime() - new Date(event.scheduled_date).getTime()) / (1000 * 60 * 60 * 24))} days
                </p>
              )}
            </div>
          </div>

          {/* Customer Information */}
          {event.customer_name && (
            <div>
              <h3 className="text-lg font-semibold text-amspm-text mb-3 flex items-center">
                <FaBuilding className="mr-2" />
                Customer
              </h3>
              <div className="bg-gray-50 dark:bg-dark-card rounded-lg p-4 space-y-2">
                <p className="text-amspm-text font-medium">{event.customer_name}</p>
                {event.customer_address && (
                  <p className="text-gray-600 dark:text-gray-400 flex items-center">
                    <FaMapMarkerAlt className="mr-2" size={14} />
                    {event.customer_address}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Assigned Users */}
          {event.user_names && event.user_names.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-amspm-text mb-3 flex items-center">
                <FaUsers className="mr-2" />
                Assigned Users
              </h3>
              <div className="bg-gray-50 dark:bg-dark-card rounded-lg p-4">
                <div className="space-y-2">
                  {event.user_names.map((userName, index) => (
                    <div key={index} className="flex items-center">
                      <FaUser className="mr-2 text-gray-400" size={14} />
                      <span className="text-amspm-text">{userName}</span>
                      {event.user_emails && event.user_emails[index] && (
                        <span className="text-gray-500 text-sm ml-2">({event.user_emails[index]})</span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Completion Information */}
          {event.status === 'completed' && event.completed_at && (
            <div>
              <h3 className="text-lg font-semibold text-amspm-text mb-3 flex items-center">
                <FaCheckCircle className="mr-2 text-green-600" />
                Completion Details
              </h3>
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 space-y-2">
                <p className="text-amspm-text">
                  <span className="font-medium">Completed on:</span> {formatDate(event.completed_at)}
                </p>
                {event.completed_by_name && (
                  <p className="text-amspm-text">
                    <span className="font-medium">Completed by:</span> {event.completed_by_name}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Document Information */}
          {event.document && (
            <div>
              <h3 className="text-lg font-semibold text-amspm-text mb-3 flex items-center">
                <FaFileAlt className="mr-2" />
                Related Document
              </h3>
              <div className="bg-gray-50 dark:bg-dark-card rounded-lg p-4">
                <p className="text-amspm-text font-medium">{event.document.document_type}</p>
                <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                  Filename: {event.document.name}
                </p>
                {event.document.expiry_date && (
                  <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                    Expires: {new Date(event.document.expiry_date).toLocaleDateString('nl-NL')}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        {(canEdit || canComplete) && (
          <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
            {canComplete && event.status === 'pending' && (
              <button
                onClick={onComplete}
                className="btn btn-primary flex items-center"
              >
                <FaCheckCircle className="mr-2" />
                Complete Event
              </button>
            )}
            {canEdit && (
              <button
                onClick={onEdit}
                className="btn btn-secondary flex items-center"
              >
                <FaEdit className="mr-2" />
                Edit Event
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EventDetailsModal;
