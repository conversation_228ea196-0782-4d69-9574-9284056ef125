"""
Minimal Flask app for testing deployment
"""
from flask import Flask
import os

# Create a simple Flask app
app = Flask(__name__)

@app.route('/')
def hello():
    return {'message': 'Customer Management API is running', 'status': 'ok'}

@app.route('/api/health')
def health():
    return {'status': 'healthy', 'message': 'API is operational'}

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)
