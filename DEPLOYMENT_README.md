# Customer Management System - Production Deployment Guide

This guide provides step-by-step instructions for deploying the Customer Management System to production using Render for the backend and Firebase Hosting for the frontend.

## Architecture Overview

- **Backend**: Flask API deployed on Render
- **Database**: PostgreSQL on Render
- **Cache**: Redis on Render (optional but recommended)
- **Frontend**: React SPA deployed on Firebase Hosting
- **Authentication**: Firebase Auth
- **File Storage**: Firebase Storage

## Prerequisites

### Required Tools
- Node.js (v16 or higher)
- Python (3.11)
- Git
- Firebase CLI (`npm install -g firebase-tools`)

### Required Accounts
- [Render](https://render.com) account
- [Firebase](https://firebase.google.com) project
- GitHub account (for code repository)

## Quick Start

1. **Run the automated deployment script:**
   ```bash
   python deploy.py
   ```

2. **Follow the deployment checklist:**
   See `DEPLOYMENT_CHECKLIST.md` for detailed steps.

## Manual Deployment Steps

### Phase 1: Firebase Setup

1. **Create Firebase Project:**
   ```bash
   firebase login
   firebase projects:create your-project-id
   firebase use your-project-id
   ```

2. **Enable Firebase Services:**
   - Authentication (Email/Password)
   - Firestore (if needed)
   - Storage
   - Hosting

3. **Generate Service Account Key:**
   - Go to Firebase Console > Project Settings > Service Accounts
   - Generate new private key
   - Save the JSON file securely

4. **Configure Firebase Storage Rules:**
   ```javascript
   rules_version = '2';
   service firebase.storage {
     match /b/{bucket}/o {
       match /{allPaths=**} {
         allow read, write: if request.auth != null;
       }
     }
   }
   ```

### Phase 2: Backend Deployment on Render

1. **Push Code to GitHub:**
   ```bash
   git add .
   git commit -m "Prepare for production deployment"
   git push origin main
   ```

2. **Create Render Services:**
   - PostgreSQL Database
   - Redis Cache (optional)
   - Web Service (Backend API)

3. **Configure Environment Variables:**
   Set these in your Render web service:
   ```
   FLASK_ENV=production
   FLASK_DEBUG=False
   SECRET_KEY=[auto-generated]
   DATABASE_URL=[auto-set by PostgreSQL service]
   FIREBASE_CREDENTIALS_JSON=[your service account JSON as string]
   FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
   FRONTEND_URL=https://your-project.web.app
   ADMIN_EMAIL=<EMAIL>
   REDIS_URL=[auto-set by Redis service]
   RATE_LIMIT_ENABLED=True
   SANITIZE_RESPONSES=True
   ```

4. **Deploy Backend:**
   - Connect GitHub repository
   - Set build command: `cd backend && pip install -r requirements.txt`
   - Set start command: `cd backend && gunicorn --bind 0.0.0.0:$PORT app:app`
   - Deploy

5. **Initialize Database:**
   ```bash
   # Run this once after deployment
   python backend/init_production_db.py
   ```

### Phase 3: Frontend Deployment

1. **Configure Environment Variables:**
   Create `frontend/.env.production`:
   ```
   VITE_API_URL=https://your-backend.onrender.com/api
   VITE_FIREBASE_API_KEY=your-api-key
   VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your-project-id
   VITE_FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
   VITE_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
   VITE_FIREBASE_APP_ID=your-app-id
   ```

2. **Build and Deploy:**
   ```bash
   cd frontend
   npm ci
   npm run build
   firebase deploy --only hosting
   ```

## Security Configuration

### Backend Security
- HTTPS enforced by Render
- CORS configured for production domains
- CSRF protection enabled
- Rate limiting active
- Security headers configured
- Response sanitization enabled

### Frontend Security
- HTTPS enforced by Firebase Hosting
- Content Security Policy configured
- XSS protection headers
- Secure Firebase configuration

### Database Security
- Encryption at rest (Render)
- SSL connections
- Proper user permissions
- Regular backups

## Environment Variables Reference

### Backend Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `FLASK_ENV` | Yes | Set to `production` |
| `FLASK_DEBUG` | Yes | Set to `False` |
| `SECRET_KEY` | Yes | Flask secret key (auto-generated) |
| `DATABASE_URL` | Yes | PostgreSQL connection string |
| `FIREBASE_CREDENTIALS_JSON` | Yes | Service account JSON as string |
| `FIREBASE_STORAGE_BUCKET` | Yes | Firebase storage bucket name |
| `FRONTEND_URL` | Recommended | Frontend domain for CORS |
| `ADMIN_EMAIL` | Recommended | Initial admin user email |
| `REDIS_URL` | Optional | Redis connection string |
| `RATE_LIMIT_ENABLED` | Optional | Enable rate limiting (default: True) |
| `SANITIZE_RESPONSES` | Optional | Enable response sanitization (default: True) |

### Frontend Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `VITE_API_URL` | Yes | Backend API URL |
| `VITE_FIREBASE_API_KEY` | Yes | Firebase web API key |
| `VITE_FIREBASE_AUTH_DOMAIN` | Yes | Firebase auth domain |
| `VITE_FIREBASE_PROJECT_ID` | Yes | Firebase project ID |
| `VITE_FIREBASE_STORAGE_BUCKET` | Yes | Firebase storage bucket |
| `VITE_FIREBASE_MESSAGING_SENDER_ID` | Yes | Firebase messaging sender ID |
| `VITE_FIREBASE_APP_ID` | Yes | Firebase app ID |
| `VITE_FIREBASE_MEASUREMENT_ID` | Optional | Firebase analytics measurement ID |

## Monitoring and Maintenance

### Health Checks
- Backend health endpoint: `/api/health`
- Monitor response times and error rates
- Set up alerts for service downtime

### Logging
- Application logs available in Render dashboard
- Error tracking and monitoring
- Performance metrics

### Backups
- Database backups handled by Render
- Firebase Storage has built-in redundancy
- Regular configuration backups recommended

### Updates
- Monitor for security updates
- Test updates in staging environment
- Use blue-green deployment for zero downtime

## Troubleshooting

### Common Issues

1. **CORS Errors:**
   - Check `FRONTEND_URL` environment variable
   - Verify allowed origins in backend configuration
   - Ensure protocol (https/http) matches

2. **Authentication Issues:**
   - Verify Firebase configuration on both frontend and backend
   - Check Firebase service account permissions
   - Validate JWT token handling

3. **Database Connection Issues:**
   - Verify `DATABASE_URL` format
   - Check PostgreSQL service status
   - Validate database permissions

4. **File Upload Issues:**
   - Check Firebase Storage configuration
   - Verify service account permissions
   - Test Firebase Storage rules

### Getting Help

1. Check the deployment checklist: `DEPLOYMENT_CHECKLIST.md`
2. Run security validation: `python backend/security_check.py`
3. Check service logs in Render dashboard
4. Verify Firebase configuration in Firebase Console

## Performance Optimization

### Backend Optimization
- Redis cache configured
- Database queries optimized
- Proper indexing on database tables
- Gunicorn workers configured appropriately

### Frontend Optimization
- Build optimization enabled
- Static assets cached
- Code splitting implemented
- Bundle size optimized

## Scaling Considerations

### Horizontal Scaling
- Increase Gunicorn workers
- Use Render's auto-scaling features
- Consider load balancing for high traffic

### Database Scaling
- Monitor database performance
- Consider read replicas for high read loads
- Optimize queries and add indexes as needed

### Cache Scaling
- Use Redis for session storage
- Implement application-level caching
- Consider CDN for static assets

## Security Best Practices

1. **Regular Security Updates:**
   - Keep dependencies updated
   - Monitor security advisories
   - Apply patches promptly

2. **Access Control:**
   - Use strong passwords
   - Enable two-factor authentication
   - Limit admin access

3. **Data Protection:**
   - Encrypt sensitive data
   - Use HTTPS everywhere
   - Implement proper backup strategies

4. **Monitoring:**
   - Set up security monitoring
   - Log security events
   - Monitor for suspicious activity
