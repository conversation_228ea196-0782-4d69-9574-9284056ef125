import <PERSON>z<PERSON><PERSON> from 'pizzip';
import Docxtemplater from 'docxtemplater';
import { saveAs } from 'file-saver';

import { getTemplateFileUrl } from './documentTemplateService';

/**
 * Service for handling document templates using Docxtemplater
 */
export class TemplateService {
  /**
   * Loads a template from a URL and returns the template as an ArrayBuffer
   * @param templateId ID of the template to load
   * @returns Promise resolving to the template ArrayBuffer
   */
  static async loadTemplate(templateId: number): Promise<ArrayBuffer> {
    try {
      const response = await fetch(getTemplateFileUrl(templateId));
      if (!response.ok) {
        throw new Error(`Failed to load template: ${response.statusText}`);
      }
      return await response.arrayBuffer();
    } catch (error) {
      console.error('Error loading template:', error);
      throw error;
    }
  }

  /**
   * Fills a template with data and returns the filled document as a blob
   * @param templateContent Template content as ArrayBuffer
   * @param data Data to fill the template with
   * @returns Promise resolving to the filled document blob
   */
  static async fillTemplate(templateContent: ArrayBuffer, data: Record<string, any>): Promise<Blob> {
    try {
      console.log('Original data:', data);

      // Load the template
      const zip = new PizZip(templateContent);

      // No template transformation needed - using base64 strings as text

      // Process the template data and handle signatures
      const processedData = await this.processTemplateData(data);

      console.log('Processed data:', processedData);

      // No image module needed - we're just placing base64 strings as text

      // Create a new instance of Docxtemplater
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true,
        delimiters: {
          start: '{',
          end: '}'
        },
        nullGetter: (_part: any) => {
          // Return empty string for any null/undefined values
          return '';
        }
      });

      // Render the document (replace all variables with their values)
      try {
        console.log('About to render document with data:', Object.keys(processedData));
        doc.render(processedData);
        console.log('Document rendered successfully');
      } catch (renderError: any) {
        console.error('Error during document rendering:', renderError);

        // Check if it's an image module error
        if (renderError.message && renderError.message.includes('image')) {
          console.error('Image module error details:', renderError);
        }

        throw new Error(`Document rendering failed: ${renderError.message || renderError}`);
      }

      // Get the zip document containing the filled template
      const out = doc.getZip().generate({
        type: 'blob',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      });

      return out;
    } catch (error) {
      console.error('Error filling template:', error);
      throw error;
    }
  }



  /**
   * Process template data to handle signatures and other special fields
   * @param data Raw template data
   * @returns Processed data ready for template filling
   */
  private static async processTemplateData(data: Record<string, any>): Promise<Record<string, any>> {
    const processedData = { ...data };

    // Process signature fields
    for (const [key, value] of Object.entries(processedData)) {
      if (key.includes('handtekening')) {
        if (typeof value === 'string' && value.startsWith('data:image/')) {
          // Validate the signature data
          try {
            const base64Data = value.split(',')[1];
            if (base64Data && base64Data.length > 0) {
              // Test if base64 is valid
              atob(base64Data);
              console.log(`Valid signature found for field: ${key} (${base64Data.length} chars)`);

              // Keep the base64 signature data as-is for direct text placement
              processedData[key] = value;

            } else {
              console.warn(`Invalid base64 data for signature field: ${key}`);
              this.setEmptySignature(processedData, key);
            }
          } catch (error) {
            console.error(`Error validating signature for field ${key}:`, error);
            this.setEmptySignature(processedData, key);
          }
        } else if (value === '' || value === null || value === undefined) {
          // Empty signature - this is valid, just set to empty string
          console.log(`Empty signature for field: ${key}`);
          this.setEmptySignature(processedData, key);
        } else {
          // Invalid signature format
          console.warn(`Invalid signature format for field: ${key}`, typeof value, value);
          this.setEmptySignature(processedData, key);
        }
      }
    }

    console.log('Processed template data:', Object.keys(processedData).filter(k => k.includes('handtekening')).map(k => `${k}: ${processedData[k] ? 'has signature' : 'empty'}`));

    return processedData;
  }

  /**
   * Helper function to set empty signature values
   * @param processedData The data object to update
   * @param key The signature field key
   */
  private static setEmptySignature(processedData: Record<string, any>, key: string): void {
    processedData[key] = '';
  }



  /**
   * Downloads a filled template
   * @param blob Blob containing the filled template
   * @param fileName Name of the file to download
   */
  static downloadDocument(blob: Blob, fileName: string): void {
    saveAs(blob, fileName);
  }

  /**
   * Saves a filled template to the server
   * @param templateId ID of the template used
   * @param customerId ID of the customer
   * @param blob Blob containing the filled template
   * @param fileName Name of the file to save
   * @returns Promise resolving to the saved document
   */
  static async saveDocument(templateId: number, customerId: number, blob: Blob, fileName: string): Promise<any> {
    const { saveFilledTemplate } = await import('./documentTemplateService');
    return saveFilledTemplate(templateId, customerId, blob, fileName);
  }

  /**
   * Analyzes a template to extract form fields
   * @param templateContent Template content as ArrayBuffer
   * @returns Promise resolving to an object with field information
   */
  static async analyzeTemplate(templateContent: ArrayBuffer): Promise<{
    fields: Array<{name: string, label: string}>;
    checkboxes: Array<{name: string, label: string}>;
    structure?: any;
    isInspectionTemplate?: boolean;
  }> {
    try {
      // Load the template
      const zip = new PizZip(templateContent);

      // Get the main document content
      const content = zip.files['word/document.xml']?.asText() || '';

      // Check if this is an inspection template based on content
      const isInspectionTemplate = content.includes('Type installatie') ||
                                   content.includes('Centrale / kiezer') ||
                                   content.includes('inbraakmeldsysteem') ||
                                   content.includes('centrale_accu');

      if (isInspectionTemplate) {
        return this.analyzeInspectionTemplate(content);
      }

      // Extract fields using regex for regular templates
      const fieldsMap = new Map<string, string>();
      const checkboxesMap = new Map<string, string>();

      // Look for simple fields in the format {field_name}
      const simpleFieldRegex = /{([^{}#/]+)}/g;
      let match;

      while ((match = simpleFieldRegex.exec(content)) !== null) {
        const fieldName = match[1].trim();
        // Skip if it's part of a conditional
        if (!content.includes(`{#if ${fieldName}}`) && !fieldName.includes('if') && !fieldName.includes('else')) {
          fieldsMap.set(fieldName, this.formatFieldLabel(fieldName));
        }
      }

      // Look for conditional fields (checkboxes) in the format {#field_name} or {#if field_name}
      const conditionalFieldRegex = /{#(?:if\s+)?([^{}]+)}/g;

      while ((match = conditionalFieldRegex.exec(content)) !== null) {
        const fieldName = match[1].trim();
        // Skip if it contains "else" or other template keywords
        if (!fieldName.includes('else') && !fieldName.includes('/') && !fieldName.includes('^')) {
          checkboxesMap.set(fieldName, this.formatFieldLabel(fieldName));
        }
      }

      // Convert maps to arrays
      const fields = Array.from(fieldsMap).map(([name, label]) => ({ name, label }));
      const checkboxes = Array.from(checkboxesMap).map(([name, label]) => ({ name, label }));

      return { fields, checkboxes };
    } catch (error) {
      console.error('Error analyzing template:', error);
      throw error;
    }
  }

  /**
   * Analyzes inspection template structure
   */
  static analyzeInspectionTemplate(_content: string) {
    const structure = {
      customerFields: [
        { name: 'bonnummer', label: 'Bonnummer', type: 'text', autoPopulate: true },
        { name: 'klantnummer', label: 'Klantnummer', type: 'text', autoPopulate: true },
        { name: 'bedrijf', label: 'Bedrijf', type: 'text', autoPopulate: true },
        { name: 'adres', label: 'Adres', type: 'text', autoPopulate: true },
        { name: 'telefoon', label: 'Telefoonnummer', type: 'text', autoPopulate: true },
        { name: 'contactpersoon', label: 'Contactpersoon', type: 'text', autoPopulate: true },
        { name: 'email', label: 'Email', type: 'text', autoPopulate: true },
        { name: 'type', label: 'Type', type: 'text', autoPopulate: false }
      ],
      installationTypes: [
        { name: 'inbraakmeldsysteem', label: 'Inbraakmeldsysteem' },
        { name: 'brandmeldsysteem', label: 'Brandmeldsysteem' },
        { name: 'cctv', label: 'CCTV' }
      ],
      sections: [
        {
          title: 'Centrale / kiezer',
          components: [
            { name: 'centrale_accu', label: 'Accu' },
            { name: 'centrale_voeding', label: 'Voeding' },
            { name: 'centrale_lusspanning', label: 'Lusspanning' },
            { name: 'centrale_uitlezing', label: 'Uitlezing' },
            { name: 'centrale_algemeen', label: 'Algemene werking' }
          ]
        },
        {
          title: 'Detectie',
          components: [
            { name: 'detectie_bevestiging', label: 'Bevestiging' },
            { name: 'detectie_werking', label: 'Werking' },
            { name: 'detectie_projectie', label: 'Projectie' },
            { name: 'detectie_algemeen', label: 'Detectie algemeen' }
          ]
        },
        {
          title: 'Bekabeling',
          components: [
            { name: 'bekabeling_bevestiging', label: 'Bevestiging' },
            { name: 'bekabeling_afscherming', label: 'Afscherming' }
          ]
        },
        {
          title: 'Signalering',
          components: [
            { name: 'signalering_bevestiging', label: 'Bevestiging' },
            { name: 'signalering_werking_flits', label: 'Werking flits' },
            { name: 'signalering_werking_sirene', label: 'Werking sirene' },
            { name: 'signalering_algemeen', label: 'Signalering algemeen' }
          ]
        },
        {
          title: 'Doormelding',
          components: [
            { name: 'doormelding_schakel', label: 'Schakelmelding' },
            { name: 'doormelding_inbraak', label: 'Inbraak' },
            { name: 'doormelding_overval', label: 'Overval' },
            { name: 'doormelding_brand', label: 'Brand' },
            { name: 'doormelding_technisch', label: 'Technisch' },
            { name: 'doormelding_contact', label: 'Contact gewenst MK' }
          ]
        }
      ],
      generalState: [
        { name: 'installatie_in_orde', label: 'Installatie in orde' },
        { name: 'installatie_niet_in_orde', label: 'Installatie niet in orde' },
        { name: 'ja_opmerking', label: 'Ja, opmerking', type: 'textarea' },
        { name: 'nee_onbekend', label: 'Nee / onbekend', type: 'textarea' }
      ],
      signatures: [
        { name: 'klant_naam', label: 'Klant naam', type: 'text' },
        { name: 'datum', label: 'Datum', type: 'date' },
        { name: 'klant_handtekening', label: 'Klant handtekening', type: 'signature' },
        { name: 'monteur_naam', label: 'Monteur naam', type: 'text' },
        { name: 'begin_tijd', label: 'Begin tijd', type: 'time' },
        { name: 'eind_tijd', label: 'Eind tijd', type: 'time' },
        { name: 'monteur_handtekening', label: 'Monteur handtekening', type: 'signature' }
      ]
    };

    return {
      fields: [],
      checkboxes: [],
      structure,
      isInspectionTemplate: true
    };
  }

  /**
   * Formats a field name into a human-readable label
   * @param fieldName The raw field name from the template
   * @returns A formatted label
   */
  static formatFieldLabel(fieldName: string): string {
    // Remove prefixes like "check_"
    let label = fieldName.replace(/^check_/, '');

    // Replace underscores with spaces
    label = label.replace(/_/g, ' ');

    // Capitalize first letter of each word
    label = label.replace(/\b\w/g, c => c.toUpperCase());

    return label;
  }
}

export default TemplateService;
