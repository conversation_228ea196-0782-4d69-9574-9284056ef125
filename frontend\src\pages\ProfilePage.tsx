import React, { useState } from "react";
import { useAuth } from "../context/AuthContext";
import { updateCurrentUserName, updateCurrentUserPassword } from "../services/userService";
import { FaUser, FaLock, FaSave, FaTimes, FaCheck } from "react-icons/fa";
import { useConfirmation } from "../context/ConfirmationContext";
import Breadcrumbs from "../components/Breadcrumbs";
import { MobileContainer, MobilePageHeader, MobileFormGroup, MobileFormActions } from "../components/common/MobileUtils";
import { useMobile } from "../hooks/useMobile";

const ProfilePage: React.FC = () => {
  const { user, setUser } = useAuth();
  const { showConfirmation } = useConfirmation();
  const { isMobile } = useMobile();

  const [editingName, setEditingName] = useState(false);
  const [name, setName] = useState(user?.name || "");
  const [originalName, setOriginalName] = useState(user?.name || "");

  const [editingPassword, setEditingPassword] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const [nameError, setNameError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const startEditingName = () => {
    setEditingName(true);
    setName(user?.name || "");
    setOriginalName(user?.name || "");
    setNameError(null);
  };

  const cancelEditingName = () => {
    setEditingName(false);
    setName(originalName);
    setNameError(null);
  };

  const startEditingPassword = () => {
    setEditingPassword(true);
    setPassword("");
    setConfirmPassword("");
    setPasswordError(null);
  };

  const cancelEditingPassword = () => {
    setEditingPassword(false);
    setPassword("");
    setConfirmPassword("");
    setPasswordError(null);
  };

  const handleSaveName = async () => {
    if (!name.trim()) {
      setNameError("Naam mag niet leeg zijn");
      return;
    }

    try {
      const updatedUser = await updateCurrentUserName(name);
      if (user) {
        setUser({ ...user, name: updatedUser.name });
      }
      setEditingName(false);
      setSuccessMessage("Naam succesvol bijgewerkt");
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error: any) {
      setNameError(error.response?.data?.error || "Fout bij het bijwerken van de naam");
    }
  };

  const handleSavePassword = async () => {
    // Reset errors
    setPasswordError(null);

    // Validate password
    if (password.length < 8) {
      setPasswordError("Wachtwoord moet minimaal 8 tekens lang zijn");
      return;
    }

    if (!/[A-Z]/.test(password)) {
      setPasswordError("Wachtwoord moet minimaal één hoofdletter bevatten");
      return;
    }

    if (!/[a-z]/.test(password)) {
      setPasswordError("Wachtwoord moet minimaal één kleine letter bevatten");
      return;
    }

    if (!/[0-9]/.test(password)) {
      setPasswordError("Wachtwoord moet minimaal één cijfer bevatten");
      return;
    }

    if (password !== confirmPassword) {
      setPasswordError("Wachtwoorden komen niet overeen");
      return;
    }

    try {
      await updateCurrentUserPassword(password);
      setEditingPassword(false);
      setPassword("");
      setConfirmPassword("");
      setSuccessMessage("Wachtwoord succesvol bijgewerkt");
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error: any) {
      setPasswordError(error.response?.data?.error || "Fout bij het bijwerken van het wachtwoord");
    }
  };

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <MobileContainer>
      {/* Breadcrumbs */}
      <Breadcrumbs />

      <MobilePageHeader
        title="Mijn Profiel"
        subtitle="Beheer uw persoonlijke gegevens en instellingen"
      />

      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
          <FaCheck className="mr-2" />
          {successMessage}
        </div>
      )}

      <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6 mb-6">
        <div className="flex items-center mb-6">
          <div className="w-16 h-16 rounded-full bg-amspm-primary text-amspm-secondary flex items-center justify-center font-bold text-2xl">
            {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
          </div>
          <div className="ml-4">
            <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text">{user.name || user.email}</h2>
            <p className="text-sm text-amspm-text-light dark:text-dark-text-light">{user.email}</p>
            <p className="text-sm text-amspm-text-light dark:text-dark-text-light capitalize">{user.role}</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* Name Section */}
          <div className="border-b pb-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text">
                <FaUser className="inline-block mr-2" /> Naam
              </h3>
              {!editingName ? (
                <button
                  onClick={startEditingName}
                  className="text-blue-600 hover:text-blue-800 dark:text-dark-accent dark:hover:text-dark-accent-hover"
                >
                  Bewerken
                </button>
              ) : null}
            </div>

            {editingName ? (
              <MobileFormGroup label="Naam" error={nameError || undefined}>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="mobile-form-input"
                  placeholder="Voer uw naam in"
                />
              </MobileFormGroup>
            ) : (
              <p className="text-amspm-text dark:text-dark-text">{user.name || "Geen naam ingesteld"}</p>
            )}

            {editingName && (
              <MobileFormActions>
                <button
                  onClick={handleSaveName}
                  className="btn btn-primary flex items-center justify-center mobile-touch-target"
                >
                  <FaSave className="mr-2" /> Opslaan
                </button>
                <button
                  onClick={cancelEditingName}
                  className="btn btn-outline flex items-center justify-center mobile-touch-target"
                >
                  <FaTimes className="mr-2" /> Annuleren
                </button>
              </MobileFormActions>
            )}
          </div>

          {/* Password Section */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text">
                <FaLock className="inline-block mr-2" /> Wachtwoord
              </h3>
              {!editingPassword ? (
                <button
                  onClick={startEditingPassword}
                  className="text-blue-600 hover:text-blue-800 dark:text-dark-accent dark:hover:text-dark-accent-hover"
                >
                  Wijzigen
                </button>
              ) : null}
            </div>

            {editingPassword ? (
              <div>
                <div className="mb-3">
                  <label className="block text-sm font-medium text-amspm-text dark:text-dark-text mb-1">
                    Nieuw wachtwoord
                  </label>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="input w-full"
                    placeholder="Voer nieuw wachtwoord in"
                  />
                </div>
                <div className="mb-3">
                  <label className="block text-sm font-medium text-amspm-text dark:text-dark-text mb-1">
                    Bevestig wachtwoord
                  </label>
                  <input
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="input w-full"
                    placeholder="Bevestig nieuw wachtwoord"
                  />
                </div>

                {passwordError && <p className="text-red-500 text-sm mb-3">{passwordError}</p>}

                <div className="text-xs text-gray-500 mb-3">
                  <p>Wachtwoord moet minimaal:</p>
                  <ul className="list-disc pl-5 mt-1">
                    <li className={password.length >= 8 ? "text-green-600" : ""}>8 tekens lang zijn</li>
                    <li className={/[A-Z]/.test(password) ? "text-green-600" : ""}>Eén hoofdletter bevatten</li>
                    <li className={/[a-z]/.test(password) ? "text-green-600" : ""}>Eén kleine letter bevatten</li>
                    <li className={/[0-9]/.test(password) ? "text-green-600" : ""}>Eén cijfer bevatten</li>
                  </ul>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={handleSavePassword}
                    className="btn btn-primary flex items-center"
                  >
                    <FaSave className="mr-2" /> Opslaan
                  </button>
                  <button
                    onClick={cancelEditingPassword}
                    className="btn btn-outline flex items-center"
                  >
                    <FaTimes className="mr-2" /> Annuleren
                  </button>
                </div>
              </div>
            ) : (
              <p className="text-amspm-text">••••••••</p>
            )}
          </div>
        </div>
      </div>
    </MobileContainer>
  );
};

export default ProfilePage;
