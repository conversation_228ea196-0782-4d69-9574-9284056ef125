# Onderhoudsbon Template Guide

## Digital Signature Implementation

The onderhoudsbon template has been updated to support digital signatures. Here's what has been implemented:

### Frontend Changes

1. **SignaturePad Component** (`frontend/src/components/SignaturePad.tsx`)
   - Uses `react-signature-canvas` for signature capture
   - Converts signatures to base64 PNG format
   - Provides clear and save functionality
   - Responsive design with proper styling

2. **InspectionTemplateForm Updates** (`frontend/src/components/InspectionTemplateForm.tsx`)
   - Added SignaturePad import
   - Updated signature fields to use SignaturePad component instead of text inputs
   - Handles signature data as base64 strings

3. **TemplateService Updates** (`frontend/src/services/templateService.ts`)
   - Added `docxtemplater-image-module` support
   - Processes signature base64 data for image insertion
   - Configures image sizing (2 inches wide by 1 inch tall)
   - Handles signature validation and processing

### Backend Changes

1. **Image Utils** (`backend/app/utils/image_utils.py`)
   - Processes base64 signature images
   - Validates signature image data
   - Optimizes images (max 400x200 pixels)
   - Converts to PNG format for better quality

2. **Document Template Service Updates** (`backend/app/services/document_template_service.py`)
   - Added signature processing method
   - Validates signature images before processing
   - Handles signature field detection (`*_handtekening`)

3. **New API Endpoint** (`backend/app/controllers/document_template_controller.py`)
   - `/api/document-templates/<id>/process-with-signatures` (POST)
   - Processes template data with signature validation
   - Returns processed data ready for document generation

### Template Structure

The signature fields in the template structure have been updated:

```javascript
signatures: [
  { name: 'klant_naam', label: 'Klant naam', type: 'text' },
  { name: 'datum', label: 'Datum', type: 'date' },
  { name: 'klant_handtekening', label: 'Klant handtekening', type: 'signature' },
  { name: 'monteur_naam', label: 'Monteur naam', type: 'text' },
  { name: 'begin_tijd', label: 'Begin tijd', type: 'time' },
  { name: 'eind_tijd', label: 'Eind tijd', type: 'time' },
  { name: 'monteur_handtekening', label: 'Monteur handtekening', type: 'signature' }
]
```

### Word Template Requirements

For the onderhoudsbon Word template to work with digital signatures, it should use:

- `{klant_handtekening}` - Will be replaced with the customer's signature image
- `{monteur_handtekening}` - Will be replaced with the technician's signature image

These placeholders will be processed by the docxtemplater image module and replaced with actual signature images in the generated document.

### Usage

1. **Customer/Monteur signs** using the signature pad in the form
2. **Signature is captured** as base64 PNG data
3. **Template is filled** using docxtemplater with image module
4. **Document is generated** with actual signature images embedded

### Testing

The implementation has been tested for:
- ✅ Frontend build compilation
- ✅ Backend import validation
- ✅ Signature component functionality
- ✅ Template processing logic

### Next Steps

To complete the implementation:
1. Create or update the actual onderhoudsbon Word template file
2. Upload the template through the document template manager
3. Test the complete flow from signature capture to document generation
4. Verify signature images appear correctly in generated documents
