#!/usr/bin/env python3
"""
Test script to debug import issues
"""
import os
import sys

print("Python version:", sys.version)
print("Current working directory:", os.getcwd())
print("Python path:", sys.path)

print("\nEnvironment variables:")
for key in ['DATABASE_URL', 'SECRET_KEY', 'FLASK_ENV', 'FIREBASE_CREDENTIALS_JSON', 'FIREBASE_STORAGE_BUCKET']:
    value = os.getenv(key)
    if value:
        print(f"{key}: {'*' * min(len(value), 20)}...")
    else:
        print(f"{key}: NOT SET")

print("\nTrying to import app module...")
try:
    import app
    print("✓ Successfully imported app module")
    print("app module attributes:", dir(app))
    
    if hasattr(app, 'app'):
        print("✓ app.app exists:", type(app.app))
    else:
        print("✗ app.app does not exist")
        
except Exception as e:
    print("✗ Failed to import app module:", str(e))
    import traceback
    traceback.print_exc()

print("\nTrying to import run module...")
try:
    import run
    print("✓ Successfully imported run module")
    print("run module attributes:", dir(run))
    
    if hasattr(run, 'app'):
        print("✓ run.app exists:", type(run.app))
    else:
        print("✗ run.app does not exist")
        
except Exception as e:
    print("✗ Failed to import run module:", str(e))
    import traceback
    traceback.print_exc()
