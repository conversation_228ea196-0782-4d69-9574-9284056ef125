#!/usr/bin/env python3
"""
Test script to verify deployment fixes work correctly.
This script tests the app creation with missing Firebase credentials.
"""

import os
import sys

def test_app_creation_without_firebase():
    """Test that the app can be created even without Firebase credentials."""
    print("🧪 Testing app creation without Firebase credentials...")
    
    # Temporarily remove Firebase environment variables
    original_firebase_json = os.environ.get('FIREBASE_CREDENTIALS_JSON')
    original_firebase_path = os.environ.get('FIREBASE_CREDENTIALS_PATH')
    original_firebase_bucket = os.environ.get('FIREBASE_STORAGE_BUCKET')
    
    # Remove Firebase env vars
    if 'FIREBASE_CREDENTIALS_JSON' in os.environ:
        del os.environ['FIREBASE_CREDENTIALS_JSON']
    if 'FIREBASE_CREDENTIALS_PATH' in os.environ:
        del os.environ['FIREBASE_CREDENTIALS_PATH']
    if 'FIREBASE_STORAGE_BUCKET' in os.environ:
        del os.environ['FIREBASE_STORAGE_BUCKET']
    
    # Set production environment
    os.environ['FLASK_ENV'] = 'production'
    os.environ['FLASK_DEBUG'] = 'False'
    
    # Set minimal required env vars
    if not os.environ.get('SECRET_KEY'):
        os.environ['SECRET_KEY'] = 'test-secret-key-for-deployment-test'
    if not os.environ.get('DATABASE_URL'):
        os.environ['DATABASE_URL'] = 'sqlite:///test.db'
    
    try:
        # Test importing the WSGI module
        print("  → Testing WSGI module import...")
        import wsgi
        print("  ✓ WSGI module imported successfully")

        # Test that wsgi.application exists
        if hasattr(wsgi, 'application'):
            print("  ✓ wsgi.application attribute exists")
            print(f"  ✓ wsgi.application type: {type(wsgi.application)}")

            # Test Firebase initialization status
            firebase_init = wsgi.application.config.get('FIREBASE_INITIALIZED', False)
            print(f"  ✓ Firebase initialized: {firebase_init}")

            if not firebase_init:
                print("  ✓ App correctly handles missing Firebase credentials")

            # Test health endpoint
            with wsgi.application.test_client() as client:
                response = client.get('/api/health')
                print(f"  ✓ Health endpoint status: {response.status_code}")
                if response.status_code == 200:
                    data = response.get_json()
                    print(f"  ✓ Health response: {data}")

        else:
            print("  ✗ wsgi.application attribute not found")
            return False
            
    except Exception as e:
        print(f"  ✗ Error during app creation test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Restore original environment variables
        if original_firebase_json:
            os.environ['FIREBASE_CREDENTIALS_JSON'] = original_firebase_json
        if original_firebase_path:
            os.environ['FIREBASE_CREDENTIALS_PATH'] = original_firebase_path
        if original_firebase_bucket:
            os.environ['FIREBASE_STORAGE_BUCKET'] = original_firebase_bucket
    
    print("  ✓ All tests passed!")
    return True

def test_gunicorn_import():
    """Test that Gunicorn can import the app correctly."""
    print("\n🧪 Testing Gunicorn WSGI import...")

    try:
        # This simulates what Gunicorn does with wsgi:application
        import wsgi
        flask_app = getattr(wsgi, 'application')
        print("  ✓ Gunicorn can access wsgi.application")
        print(f"  ✓ App type: {type(flask_app)}")
        return True
    except AttributeError as e:
        print(f"  ✗ Gunicorn import would fail: {e}")
        return False
    except Exception as e:
        print(f"  ✗ Unexpected error: {e}")
        return False

def main():
    """Run all deployment tests."""
    print("🚀 Running deployment fix tests...\n")
    
    success = True
    
    # Test 1: App creation without Firebase
    if not test_app_creation_without_firebase():
        success = False
    
    # Test 2: Gunicorn import
    if not test_gunicorn_import():
        success = False
    
    print("\n" + "="*50)
    if success:
        print("🎉 All deployment tests PASSED!")
        print("The app should now deploy successfully on Render.")
        print("\nNext steps:")
        print("1. Set the required environment variables in Render dashboard")
        print("2. Redeploy your application")
        print("3. Check the health endpoint")
    else:
        print("❌ Some tests FAILED!")
        print("Please review the errors above and fix them before deploying.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
