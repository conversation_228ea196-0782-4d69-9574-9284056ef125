# Custom Rate Limiting System

This document describes the custom rate limiting system that replaces Flask-Limiter to solve issues with global limits overriding endpoint-specific limits.

## Overview

The custom rate limiting system provides:

- **Sliding window rate limiting** - More accurate than fixed windows
- **Per-endpoint rate limits** - No conflicts between global and specific limits
- **Thread-safe implementation** - Safe for concurrent requests
- **Memory efficient** - Automatic cleanup of expired windows
- **Configurable** - Can be enabled/disabled via configuration
- **Clear error messages** - Better user experience when limits are exceeded

## Architecture

### Components

1. **SlidingWindowRateLimiter** (`app/utils/custom_rate_limiter.py`)
   - Core rate limiting logic
   - Sliding window implementation
   - Thread-safe with RLock
   - Automatic cleanup of expired windows

2. **rate_limit decorator** (`app/utils/rate_limit.py`)
   - Easy-to-use decorator for Flask routes
   - Imports from custom implementation
   - Maintains same API as before

3. **Configuration** (`app/config.py`)
   - `RATE_LIMIT_ENABLED` - Enable/disable rate limiting
   - Removed Flask-Limiter specific settings

### How It Works

1. **Client Identification**: Uses IP address + user ID (if available)
2. **Sliding Window**: Tracks request timestamps in a deque per client/endpoint
3. **Rate Check**: Removes expired entries, checks if limit exceeded
4. **Response**: Returns 429 with Retry-After header if limit exceeded

## Usage

### Basic Usage

```python
from app.utils.rate_limit import rate_limit

@app.route('/api/endpoint')
@rate_limit('60/minute')
def my_endpoint():
    return jsonify({'message': 'Hello'})
```

### Supported Rate Limit Formats

- `60/minute` - 60 requests per minute
- `100/hour` - 100 requests per hour  
- `5/second` - 5 requests per second
- `1000/day` - 1000 requests per day

### Current Rate Limits by Endpoint

| Endpoint Type | Rate Limit | Reasoning |
|---------------|------------|-----------|
| Authentication | 60/minute | Prevent brute force attacks |
| Data Retrieval (GET) | 60/minute | Normal usage patterns |
| Data Creation (POST) | 30/minute | More resource intensive |
| Data Updates (PUT) | 30/minute | More resource intensive |
| Bulk Operations | 10/minute | Very resource intensive |
| Delete All Operations | 5/minute | Extremely dangerous operations |
| Time Tracking | 120/minute | Higher usage expected |
| Quotations | 100/minute | Business critical operations |

## Configuration

### Environment Variables

```bash
# Enable/disable rate limiting
RATE_LIMIT_ENABLED=True
```

### Application Configuration

```python
# In app/config.py
RATE_LIMIT_ENABLED = os.getenv("RATE_LIMIT_ENABLED", "True").lower() in ("true", "1", "t")
```

## Error Responses

When rate limit is exceeded, the system returns:

```json
{
    "error": "Rate limit exceeded",
    "message": "Too many requests. Limit: 60/minute",
    "retry_after": 45
}
```

HTTP Status: `429 Too Many Requests`
Headers: `Retry-After: 45`

## Monitoring and Logging

### Log Levels

- **DEBUG**: Successful rate limit checks
- **WARNING**: Rate limit exceeded events
- **INFO**: System initialization and cleanup
- **ERROR**: Configuration or parsing errors

### Log Examples

```
WARNING - Rate limit exceeded for 192.168.1.100:user123 on endpoint auth.verify (limit: 60/minute)
INFO - Cleaned up 15 expired rate limit windows
DEBUG - Rate limit check passed for customer.get_all_customers (limit: 60/minute)
```

## Maintenance

### Automatic Cleanup

The system automatically cleans up expired rate limit windows every hour to prevent memory leaks.

### Manual Cleanup

```python
from app.utils.custom_rate_limiter import cleanup_rate_limiter
cleanup_rate_limiter()
```

## Testing

Run the test suite to verify the rate limiter works correctly:

```bash
cd backend
python test_rate_limiter.py
```

The test suite includes:
- Unit tests for rate limit parsing
- HTTP tests for actual rate limiting
- Concurrent request testing
- Error handling verification

## Migration from Flask-Limiter

### What Changed

1. **Removed Flask-Limiter dependency** from requirements.txt
2. **Updated imports** in controllers (removed `from app import limiter`)
3. **New configuration** options replace `RATELIMIT_DEFAULT`
4. **Same decorator API** - no changes needed in route definitions

### Benefits

- ✅ No more conflicts between global and endpoint limits
- ✅ More predictable rate limiting behavior
- ✅ Better error messages and logging
- ✅ Configurable enable/disable
- ✅ Memory efficient with automatic cleanup
- ✅ Thread-safe implementation

### Potential Issues

- ⚠️ Memory usage scales with number of unique clients
- ⚠️ Rate limits reset on application restart
- ⚠️ No built-in Redis support (can be added if needed)

## Future Enhancements

1. **Redis Backend**: For distributed rate limiting across multiple servers
2. **Rate Limit Headers**: Add `X-RateLimit-*` headers to all responses
3. **Whitelist/Blacklist**: IP-based allow/deny lists
4. **Dynamic Limits**: Adjust limits based on user roles or subscription tiers
5. **Metrics**: Integration with monitoring systems

## Troubleshooting

### Rate Limiting Not Working

1. Check if `RATE_LIMIT_ENABLED=True` in configuration
2. Verify decorator is applied to routes
3. Check logs for error messages
4. Test with the provided test script

### Memory Issues

1. Monitor rate limiter window cleanup logs
2. Adjust cleanup frequency if needed
3. Consider Redis backend for high-traffic applications

### False Positives

1. Check client identification logic
2. Verify rate limit format strings
3. Consider proxy/load balancer IP forwarding
