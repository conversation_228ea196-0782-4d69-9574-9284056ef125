"""
Document Template service module.
This module provides business logic for document templates.
"""
from typing import List, Dict, Optional, BinaryIO
import os
import logging
from app.repositories.document_template_repository import DocumentTemplateRepository
from app.schemas.document_template_schema import document_template_schema, document_templates_schema
from app.utils.image_utils import process_signature_base64, validate_signature_image
from flask import send_file
from werkzeug.utils import secure_filename

logger = logging.getLogger(__name__)

class DocumentTemplateService:
    """Service for document templates."""
    
    def __init__(self):
        """Initialize the service."""
        self.template_repo = DocumentTemplateRepository()
    
    def get_all_templates(self) -> List[Dict]:
        """Get all document templates."""
        templates = self.template_repo.get_all()
        return document_templates_schema.dump(templates)
    
    def get_template_by_id(self, template_id: int) -> Optional[Dict]:
        """Get a document template by ID."""
        template = self.template_repo.get_by_id(template_id)
        if not template:
            return None
        return document_template_schema.dump(template)
    
    def get_templates_by_document_type(self, document_type: str) -> List[Dict]:
        """Get all document templates for a specific document type."""
        templates = self.template_repo.get_by_document_type(document_type)
        return document_templates_schema.dump(templates)
    
    def create_template(self, template_data: Dict, file) -> Dict:
        """Create a new document template."""
        # Determine file type from file extension
        original_filename = file.filename or f"template_{template_data.get('document_type', 'document')}.docx"
        filename = secure_filename(original_filename)
        file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else 'docx'
        
        # Add file type to template data
        template_data['file_type'] = file_ext
        
        # Create the template
        template = self.template_repo.create(template_data, file)
        return document_template_schema.dump(template)
    
    def update_template(self, template_id: int, template_data: Dict) -> Dict:
        """Update a document template."""
        template = self.template_repo.get_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")
        
        updated_template = self.template_repo.update(template, template_data)
        return document_template_schema.dump(updated_template)
    
    def delete_template(self, template_id: int) -> bool:
        """Delete a document template."""
        template = self.template_repo.get_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")
        
        return self.template_repo.delete(template)
    
    def get_template_file(self, template_id: int):
        """Get the template file from Firebase Storage."""
        from flask import Response
        from app.utils.firebase import download_file_from_storage

        template = self.template_repo.get_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")

        # Download the file directly from Firebase Storage
        file_content, content_type = download_file_from_storage(template.file_path)

        # Return the file content directly
        return Response(
            file_content,
            mimetype=content_type,
            headers={
                'Content-Disposition': f'attachment; filename="{template.name}.{template.file_type}"',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        )

    def save_filled_template(self, customer_id: int, template_id: int, file_content: bytes, filename: str, uploaded_by: int) -> Dict:
        """Save a filled template as a new document."""
        from app.utils.firebase import upload_bytes_to_storage
        from app.services.document_service import DocumentService

        template = self.template_repo.get_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")

        # Create destination path for Firebase Storage
        destination_path = f"documents/{customer_id}/{template.document_type}/{filename}"

        # Upload to Firebase Storage with proper content type
        content_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        file_url, storage_path = upload_bytes_to_storage(
            file_content,
            destination_path,
            content_type=content_type
        )

        # Create the document using the document service
        document_service = DocumentService()

        # Create a file-like object from bytes for the document service
        import io
        file_obj = io.BytesIO(file_content)
        file_obj.filename = filename
        file_obj.seek(0)

        # Create the document record
        document = document_service.create_document_from_bytes(
            customer_id=customer_id,
            event_id=None,
            file_content=file_content,
            filename=filename,
            document_type=template.document_type,
            uploaded_by=uploaded_by,
            file_url=file_url,
            storage_path=storage_path
        )

        return document

    def process_template_with_signatures(self, template_data: Dict) -> Dict:
        """
        Process template data to handle signature images.

        Args:
            template_data: Dictionary containing template field data

        Returns:
            Dict: Processed template data with signature images handled
        """
        processed_data = template_data.copy()

        # Process signature fields
        for field_name, field_value in template_data.items():
            if field_name.endswith('_handtekening') and field_value:
                if isinstance(field_value, str) and field_value.startswith('data:image/'):
                    try:
                        # Validate signature image
                        if validate_signature_image(field_value):
                            # Keep the base64 data for docxtemplater image module
                            processed_data[field_name] = field_value
                            logger.info(f"Processed signature field: {field_name}")
                        else:
                            logger.warning(f"Invalid signature image for field: {field_name}")
                            processed_data[field_name] = ''
                    except Exception as e:
                        logger.error(f"Error processing signature {field_name}: {str(e)}")
                        processed_data[field_name] = ''
                else:
                    # If it's not a valid signature, clear it
                    processed_data[field_name] = ''

        return processed_data

    def analyze_template_structure(self, template_id: int) -> Dict:
        """Analyze a template to extract its structure and fields."""
        from app.utils.firebase import download_file_from_storage
        import zipfile
        import io
        import xml.etree.ElementTree as ET

        template = self.template_repo.get_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")

        # Download the file from Firebase Storage
        file_content, _ = download_file_from_storage(template.file_path)

        # Analyze the DOCX file
        try:
            # Create a file-like object from bytes
            file_obj = io.BytesIO(file_content)

            # Open as ZIP file (DOCX is a ZIP archive)
            with zipfile.ZipFile(file_obj, 'r') as zip_file:
                # Read the main document content
                document_xml = zip_file.read('word/document.xml').decode('utf-8')

                # Check if this is an inspection template
                is_inspection_template = (
                    'Type installatie' in document_xml or
                    'Centrale / kiezer' in document_xml or
                    'inbraakmeldsysteem' in document_xml or
                    'centrale_accu' in document_xml
                )

                # Extract all field placeholders
                import re

                # Find simple fields {field_name}
                simple_fields = re.findall(r'\{([^{}#/\^]+)\}', document_xml)

                # Find conditional fields {#field_name} and {^field_name}
                conditional_fields = re.findall(r'\{[#\^]([^{}]+)\}', document_xml)

                # Clean and deduplicate fields
                all_fields = list(set(simple_fields + conditional_fields))

                analysis = {
                    'template_id': template_id,
                    'template_name': template.name,
                    'is_inspection_template': is_inspection_template,
                    'fields': all_fields,
                    'field_count': len(all_fields),
                    'has_conditional_logic': len(conditional_fields) > 0,
                    'document_type': template.document_type
                }

                if is_inspection_template:
                    analysis['structure_type'] = 'inspection_form'
                    analysis['sections'] = [
                        'customer_info',
                        'installation_types',
                        'centrale_kiezer',
                        'detectie',
                        'bekabeling',
                        'signalering',
                        'doormelding',
                        'general_state',
                        'signatures'
                    ]
                else:
                    analysis['structure_type'] = 'simple_form'

                return analysis

        except Exception as e:
            raise Exception(f"Failed to analyze template: {str(e)}")
