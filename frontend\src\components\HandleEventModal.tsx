import React, { useState, useEffect, useRef } from 'react';
import { Event } from '../types/event';
import { handleEventSchema, validateData } from '../utils/validation';
import { useConfirmation } from '../context/ConfirmationContext';
import { DocumentTemplate } from '../types/document_template';
import { Customer } from '../types/customer';
import { getAllTemplates } from '../services/documentTemplateService';
import TemplateFormEditor from './TemplateFormEditor';
import InspectionTemplateForm from './InspectionTemplateForm';
import TemplateService from '../services/templateService';
import { FaFileUpload, FaFileAlt, FaTimes } from 'react-icons/fa';
import { MobileFormGroup, MobileFormActions, MobileButtonGroup } from './common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

interface HandleEventModalProps {
  event: Event;
  onClose: () => void;
  onCompleteWithFile: (event: Event) => void;
  onCompleteWithTemplate?: (event: Event, templateBlob: Blob, fileName: string) => void;
  submitting: boolean;
  file: File | null;
  setFile: (file: File | null) => void;
  newExpiryDate: string;
  setNewExpiryDate: (date: string) => void;
  expiryType: "date" | "niet_van_toepassing";
  setExpiryType: (type: "date" | "niet_van_toepassing") => void;
  documentNotApplicable: boolean;
  setDocumentNotApplicable: (value: boolean) => void;
  useVersionStatus: boolean;
  setUseVersionStatus: (use: boolean) => void;
  versionStatus: "active" | "inactive";
  setVersionStatus: (status: "active" | "inactive") => void;
}

const HandleEventModal: React.FC<HandleEventModalProps> = ({
  event,
  onClose,
  onCompleteWithFile,
  onCompleteWithTemplate,
  submitting,
  file,
  setFile,
  newExpiryDate,
  setNewExpiryDate,
  expiryType,
  setExpiryType,
  documentNotApplicable,
  setDocumentNotApplicable,
  useVersionStatus,
  setUseVersionStatus,
  versionStatus,
  setVersionStatus
}) => {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [formModified, setFormModified] = useState(false);
  const { showConfirmation } = useConfirmation();
  const { isMobile } = useMobile();

  // Template-related state
  const [completionMode, setCompletionMode] = useState<'file' | 'template'>('file');
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);
  const [templateContent, setTemplateContent] = useState<ArrayBuffer | null>(null);
  const [loadingTemplates, setLoadingTemplates] = useState(false);
  const [templateError, setTemplateError] = useState<string | null>(null);
  const initialStateRef = useRef({
    file,
    newExpiryDate,
    expiryType,
    documentNotApplicable,
    useVersionStatus,
    versionStatus,
    completionMode,
    selectedTemplate
  });

  // Load templates on component mount
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        setLoadingTemplates(true);
        setTemplateError(null);
        const allTemplates = await getAllTemplates();
        setTemplates(allTemplates);
      } catch (error: any) {
        console.error('Error loading templates:', error);
        setTemplateError('Failed to load templates');
      } finally {
        setLoadingTemplates(false);
      }
    };

    loadTemplates();
  }, []);

  // Load template content when a template is selected
  useEffect(() => {
    const loadTemplateContent = async () => {
      if (!selectedTemplate) {
        setTemplateContent(null);
        return;
      }

      try {
        setTemplateError(null);
        const content = await TemplateService.loadTemplate(selectedTemplate.id);
        setTemplateContent(content);
      } catch (error: any) {
        console.error('Error loading template content:', error);
        setTemplateError('Failed to load template content');
        setTemplateContent(null);
      }
    };

    loadTemplateContent();
  }, [selectedTemplate]);
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFile(e.target.files[0]);
      setFormModified(true);
    }
  };

  const handleTemplateCompletion = async (templateBlob: Blob, fileName: string) => {
    if (onCompleteWithTemplate) {
      await onCompleteWithTemplate(event, templateBlob, fileName);
    }
  };

  const handleModeChange = (mode: 'file' | 'template') => {
    setCompletionMode(mode);
    setFormModified(true);
    if (mode === 'file') {
      setSelectedTemplate(null);
      setTemplateContent(null);
    } else {
      setFile(null);
    }
  };

  // Track form modifications
  useEffect(() => {
    const currentState = {
      file,
      newExpiryDate,
      expiryType,
      documentNotApplicable,
      useVersionStatus,
      versionStatus
    };

    // Compare current state with initial state
    const hasChanges = JSON.stringify(currentState) !== JSON.stringify(initialStateRef.current);
    setFormModified(hasChanges);
  }, [file, newExpiryDate, expiryType, documentNotApplicable, useVersionStatus, versionStatus]);

  // Handle button clicks
  const handleButtonClick = async (action: string) => {
    if (action === 'complete-with-file') {
      setValidationErrors([]);

      // Validate form data based on completion mode
      if (completionMode === 'file') {
        const { isValid, errors } = await validateData(handleEventSchema, {
          file,
          expiryType,
          newExpiryDate,
          documentNotApplicable,
          useVersionStatus,
          versionStatus
        });

        if (!isValid) {
          setValidationErrors(errors);
          return;
        }

        onCompleteWithFile(event);
      } else if (completionMode === 'template') {
        // Template mode validation
        if (!selectedTemplate) {
          setValidationErrors(['Please select a template']);
          return;
        }

        // Template completion will be handled by the template form component
        // The form will call handleTemplateCompletion when ready
      }
    } else if (action === 'close') {
      if (formModified) {
        showConfirmation({
          title: "Wijzigingen negeren",
          message: "U heeft niet-opgeslagen wijzigingen. Weet u zeker dat u dit venster wilt sluiten?",
          confirmText: "Negeren",
          cancelText: "Annuleren",
          confirmButtonClass: "bg-red-600 hover:bg-red-700",
          onConfirm: () => onClose()
        });
      } else {
        onClose();
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" style={{pointerEvents: 'auto'}}>
      {/* Dark overlay */}
      <div
        className="absolute inset-0"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          handleButtonClick('close');
        }}
        style={{pointerEvents: 'auto', cursor: 'pointer'}}
      ></div>

      {/* Modal content */}
      <div
        className={`bg-white dark:bg-dark-secondary rounded-lg shadow-lg p-4 sm:p-6 w-full relative z-50 ${
          completionMode === 'template' && selectedTemplate
            ? 'max-w-4xl max-h-[90vh] overflow-y-auto'
            : 'max-w-md'
        }`}
        onClick={(e) => e.stopPropagation()}
        style={{pointerEvents: 'auto'}}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-xl font-medium text-amspm-text dark:text-dark-text">
              Gebeurtenis Afhandelen {event.id}
            </h3>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Type: {event.event_type}
            </div>
          </div>
          <button
            onClick={() => handleButtonClick('close')}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <FaTimes size={20} />
          </button>
        </div>

        {/* Mode Selection */}
        <div className="mb-6">
          <MobileButtonGroup>
            <button
              onClick={() => handleModeChange('file')}
              className={`flex items-center justify-center px-4 py-2 rounded-l-lg border ${
                completionMode === 'file'
                  ? 'bg-amspm-primary text-white border-amspm-primary'
                  : 'bg-white dark:bg-dark-card text-amspm-text dark:text-dark-text border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              disabled={submitting}
            >
              <FaFileUpload className="mr-2" />
              {isMobile ? 'Upload' : 'Bestand Uploaden'}
            </button>
            <button
              onClick={() => handleModeChange('template')}
              className={`flex items-center justify-center px-4 py-2 rounded-r-lg border-t border-r border-b ${
                completionMode === 'template'
                  ? 'bg-amspm-primary text-white border-amspm-primary'
                  : 'bg-white dark:bg-dark-card text-amspm-text dark:text-dark-text border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              disabled={submitting}
            >
              <FaFileAlt className="mr-2" />
              {isMobile ? 'Template' : 'Template Gebruiken'}
            </button>
          </MobileButtonGroup>
        </div>

        {/* Error Messages */}
        {validationErrors.length > 0 && (
          <div className="bg-yellow-100 dark:bg-yellow-900 border border-yellow-400 dark:border-yellow-600 text-yellow-700 dark:text-yellow-300 px-4 py-3 rounded mb-4">
            <ul className="list-disc pl-5">
              {validationErrors.map((err, index) => (
                <li key={index}>{err}</li>
              ))}
            </ul>
          </div>
        )}

        {templateError && (
          <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4">
            {templateError}
          </div>
        )}

        {/* Content based on mode */}
        {completionMode === 'file' ? (
          <div className="space-y-4">
            <MobileFormGroup label={`Nieuw Document Uploaden (${event.event_type})`}>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="mobile-form-input"
                disabled={submitting || documentNotApplicable}
              />
              <div className="mt-2">
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={documentNotApplicable}
                    onChange={(e) => {
                      setDocumentNotApplicable(e.target.checked);
                      if (e.target.checked) setFile(null);
                      setFormModified(true);
                    }}
                    disabled={submitting}
                    className="form-checkbox"
                  />
                  <span className="ml-2 text-amspm-text dark:text-dark-text">Document niet van toepassing</span>
                </label>
              </div>
            </MobileFormGroup>
            <MobileFormGroup label="Vervaldatum Type">
              <select
                value={expiryType}
                onChange={(e) => {
                  setExpiryType(e.target.value as "date" | "niet_van_toepassing");
                  if (e.target.value === "niet_van_toepassing") {
                    setNewExpiryDate("");
                  }
                  setFormModified(true);
                }}
                className="mobile-form-input"
                disabled={submitting || documentNotApplicable}
              >
                <option value="date">Datum</option>
                <option value="niet_van_toepassing">Niet van toepassing</option>
              </select>
            </MobileFormGroup>

            {expiryType === "date" && !documentNotApplicable && (
              <MobileFormGroup label="Vervaldatum">
                <input
                  type="datetime-local"
                  value={newExpiryDate}
                  onChange={(e) => {
                    setNewExpiryDate(e.target.value);
                    setFormModified(true);
                  }}
                  className="mobile-form-input"
                  disabled={submitting}
                />
              </MobileFormGroup>
            )}

            <MobileFormGroup label="Version Status">
              <div className="mb-2">
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={useVersionStatus}
                    onChange={(e) => {
                      setUseVersionStatus(e.target.checked);
                      setFormModified(true);
                    }}
                    disabled={submitting}
                    className="form-checkbox"
                  />
                  <span className="ml-2 text-amspm-text dark:text-dark-text">Use Version Status</span>
                </label>
              </div>
              {useVersionStatus && (
                <select
                  value={versionStatus}
                  onChange={(e) => {
                    setVersionStatus(e.target.value as "active" | "inactive");
                    setFormModified(true);
                  }}
                  className="mobile-form-input"
                  disabled={submitting}
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              )}
            </MobileFormGroup>
          </div>
        ) : (
          // Template mode
          <div className="space-y-4">
            {loadingTemplates ? (
              <div className="text-center py-4">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-amspm-primary"></div>
                <p className="mt-2 text-amspm-text dark:text-dark-text">Templates laden...</p>
              </div>
            ) : (
              <>
                <MobileFormGroup label="Selecteer Template">
                  <select
                    value={selectedTemplate?.id || ''}
                    onChange={(e) => {
                      const templateId = parseInt(e.target.value);
                      const template = templates.find(t => t.id === templateId) || null;
                      setSelectedTemplate(template);
                      setFormModified(true);
                    }}
                    className="mobile-form-input"
                    disabled={submitting}
                  >
                    <option value="">Kies een template...</option>
                    {templates.map(template => (
                      <option key={template.id} value={template.id}>
                        {template.name} ({template.document_type})
                      </option>
                    ))}
                  </select>
                </MobileFormGroup>

                {selectedTemplate && templateContent && (
                  <div className="border-t pt-4">
                    {selectedTemplate.document_type === 'inspectie' ? (
                      <InspectionTemplateForm
                        template={selectedTemplate}
                        templateContent={templateContent}
                        customer={event.customer_id ? { id: event.customer_id, name: event.customer_name || 'Unknown' } : undefined}
                        onSave={handleTemplateCompletion}
                        onCancel={() => setSelectedTemplate(null)}
                      />
                    ) : (
                      <TemplateFormEditor
                        template={selectedTemplate}
                        customer={event.customer_id ? { id: event.customer_id, name: event.customer_name || 'Unknown' } : undefined}
                        onSave={handleTemplateCompletion}
                        onCancel={() => setSelectedTemplate(null)}
                      />
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        )}
        {/* Action Buttons - Only show for file mode or when no template is selected */}
        {(completionMode === 'file' || !selectedTemplate) && (
          <MobileFormActions>
            <button
              type="button"
              onClick={() => handleButtonClick('complete-with-file')}
              className="btn btn-primary flex-1 mobile-touch-target"
              disabled={submitting || (completionMode === 'template' && !selectedTemplate)}
            >
              {submitting
                ? "Bezig..."
                : completionMode === 'template'
                ? "Template Selecteren"
                : documentNotApplicable && expiryType === "niet_van_toepassing"
                ? "Gebeurtenis Voltooien"
                : "Voltooien"}
            </button>
            <button
              type="button"
              onClick={() => handleButtonClick('close')}
              className="btn btn-outline flex-1 mobile-touch-target"
              disabled={submitting}
            >
              Annuleren
            </button>
          </MobileFormActions>
        )}
      </div>
    </div>
  );
};

export default HandleEventModal;
