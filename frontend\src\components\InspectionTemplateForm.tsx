import React, { useState, useEffect } from 'react';
import { DocumentTemplate } from '../types/document_template';
import { Customer } from '../types/customer';
import { FaSave, FaDownload, FaSpinner } from 'react-icons/fa';
import TemplateService from '../services/templateService';
import SignaturePad from './SignaturePad';
import { MobileCard, MobileFormGroup, MobileFormActions, MobileButtonGroup } from './common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

interface InspectionTemplateFormProps {
  template: DocumentTemplate;
  customer?: Customer;
  onSave: (blob: Blob, fileName: string) => void;
  onCancel: () => void;
}

interface FormData {
  [key: string]: any;
}

const InspectionTemplateForm: React.FC<InspectionTemplateFormProps> = ({
  template,
  customer,
  onSave,
  onCancel
}) => {
  const { isMobile } = useMobile();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [templateContent, setTemplateContent] = useState<ArrayBuffer | null>(null);
  const [generating, setGenerating] = useState<boolean>(false);
  const [structure, setStructure] = useState<any>(null);
  const [formData, setFormData] = useState<FormData>({});
  const [activeTab, setActiveTab] = useState<string>('customer');

  useEffect(() => {
    loadTemplate();
  }, [template.id]);

  useEffect(() => {
    if (customer && structure) {
      populateCustomerData();
    }
  }, [customer, structure]);

  const loadTemplate = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load the template
      const content = await TemplateService.loadTemplate(template.id);
      setTemplateContent(content);

      // Analyze the template to extract structure
      const analysis = await TemplateService.analyzeTemplate(content);
      
      if (analysis.isInspectionTemplate && analysis.structure) {
        setStructure(analysis.structure);
        initializeFormData(analysis.structure);
      } else {
        throw new Error('This is not a valid inspection template');
      }
    } catch (err: any) {
      setError(`Failed to load template: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const initializeFormData = (structure: any) => {
    const initialData: FormData = {};

    // Initialize customer fields
    structure.customerFields.forEach((field: any) => {
      initialData[field.name] = '';
    });

    // Initialize installation types
    structure.installationTypes.forEach((type: any) => {
      initialData[type.name] = false;
    });

    // Initialize section components
    structure.sections.forEach((section: any) => {
      section.components.forEach((component: any) => {
        initialData[`${component.name}_goed`] = false;
        initialData[`${component.name}_fout`] = false;
        initialData[`${component.name}_nvt`] = false;
        initialData[`${component.name}_opmerkingen`] = '';
      });
    });

    // Initialize general state
    structure.generalState.forEach((field: any) => {
      if (field.type === 'textarea') {
        initialData[field.name] = '';
      } else {
        initialData[field.name] = false;
      }
    });

    // Initialize signatures
    structure.signatures.forEach((field: any) => {
      initialData[field.name] = '';
    });

    // Set current date
    initialData.datum = new Date().toISOString().split('T')[0];

    setFormData(initialData);
  };

  const populateCustomerData = () => {
    if (!customer || !structure) return;

    const updatedData = { ...formData };

    // Map customer data to form fields
    const customerMapping = {
      bonnummer: customer.code || '',
      klantnummer: customer.code || '',
      bedrijf: customer.name || '',
      adres: `${customer.address || ''} ${customer.postal_code || ''} ${customer.city || ''}`.trim(),
      telefoon: customer.phone || customer.mobile || '',
      contactpersoon: customer.contact_person || '',
      email: customer.email || ''
    };

    Object.entries(customerMapping).forEach(([key, value]) => {
      if (structure.customerFields.find((f: any) => f.name === key && f.autoPopulate)) {
        updatedData[key] = value;
      }
    });

    setFormData(updatedData);
  };

  const handleFieldChange = (fieldName: string, value: any) => {
    // Debug signature fields
    if (fieldName.includes('handtekening')) {
      console.log(`Signature field ${fieldName} changed:`, value ? 'has signature data' : 'empty');
    }

    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const handleRadioGroupChange = (groupName: string, selectedValue: string) => {
    const updatedData = { ...formData };
    
    // Clear all options in the group
    ['goed', 'fout', 'nvt'].forEach(option => {
      updatedData[`${groupName}_${option}`] = false;
    });
    
    // Set the selected option
    updatedData[`${groupName}_${selectedValue}`] = true;
    
    setFormData(updatedData);
  };

  const createTemplateData = () => {
    const data: Record<string, any> = { ...formData };

    // Ensure all boolean values are properly set for DocxTemplater
    Object.keys(data).forEach(key => {
      if (typeof data[key] === 'boolean') {
        // DocxTemplater expects boolean values for conditional rendering
        data[key] = data[key];
      }
    });

    return data;
  };

  const handleSave = async () => {
    if (!templateContent) return;

    try {
      setGenerating(true);
      setError(null);

      // Create template data from form
      const data = createTemplateData();

      // Fill the template with the data
      const docxBlob = await TemplateService.fillTemplate(templateContent, data);

      // Generate a filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const customerName = customer?.name?.replace(/\s+/g, '_') || 'unknown';
      const fileName = `Inspectie_${customerName}_${timestamp}.docx`;

      // Pass the blob and filename to the parent component
      onSave(docxBlob, fileName);
    } catch (err: any) {
      setError(`Failed to generate document: ${err.message || 'Unknown error'}`);
    } finally {
      setGenerating(false);
    }
  };

  const handleDownload = async () => {
    if (!templateContent) return;

    try {
      setGenerating(true);
      setError(null);

      // Create template data from form
      const data = createTemplateData();

      // Fill the template with the data
      const docxBlob = await TemplateService.fillTemplate(templateContent, data);

      // Generate a filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const customerName = customer?.name?.replace(/\s+/g, '_') || 'unknown';
      const fileName = `Inspectie_${customerName}_${timestamp}.docx`;

      // Download the document
      TemplateService.downloadDocument(docxBlob, fileName);
    } catch (err: any) {
      setError(`Failed to download document: ${err.message || 'Unknown error'}`);
    } finally {
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin text-amspm-primary text-2xl" />
        <span className="ml-2">Loading inspection template...</span>
      </div>
    );
  }

  if (!structure) {
    return (
      <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-4">
        <div className="text-center text-red-500">
          <p>Failed to load inspection template structure</p>
          <button onClick={onCancel} className="btn btn-outline btn-sm mt-4">
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'customer', label: 'Klant Gegevens', icon: '👤' },
    { id: 'installation', label: 'Type Installatie', icon: '🔧' },
    { id: 'inspection', label: 'Inspectie', icon: '🔍' },
    { id: 'state', label: 'Algemene Staat', icon: '📋' },
    { id: 'signatures', label: 'Akkoord', icon: '✍️' }
  ];

  return (
    <MobileCard>
      {/* Header */}
      <div className={`${isMobile ? 'flex flex-col space-y-4' : 'flex justify-between items-center'} p-4 border-b border-gray-200 dark:border-gray-700`}>
        <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text">
          {template.name} - {customer?.name || 'Nieuwe Inspectie'}
        </h3>
        {!isMobile && (
          <div className="flex space-x-2">
            <button
              onClick={onCancel}
              className="btn btn-outline btn-sm mobile-touch-target"
              disabled={generating}
            >
              Annuleren
            </button>
            <button
              onClick={handleDownload}
              className="btn btn-outline btn-sm mobile-touch-target"
              disabled={generating}
              title="Download Document"
            >
              <FaDownload className="mr-1" /> Download
            </button>
            <button
              onClick={handleSave}
              className="btn btn-primary btn-sm mobile-touch-target"
              disabled={generating}
            >
              {generating ? (
                <><FaSpinner className="animate-spin mr-1" /> Opslaan</>
              ) : (
                <><FaSave className="mr-1" /> Opslaan</>
              )}
            </button>
          </div>
        )}
      </div>

      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500">
          <p className="text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className={`flex ${isMobile ? 'overflow-x-auto mobile-scroll-container' : 'space-x-8'} px-4`} aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap mobile-touch-target ${isMobile ? 'flex-shrink-0 mr-6' : ''} ${
                activeTab === tab.id
                  ? 'border-amspm-primary text-amspm-primary dark:text-dark-accent dark:border-dark-accent'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
              disabled={generating}
            >
              <span className="mr-2">{tab.icon}</span>
              {isMobile ? tab.icon : tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {/* Customer Information Tab */}
        {activeTab === 'customer' && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4">Klant Gegevens</h4>
            <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'} gap-4`}>
              {structure.customerFields.map((field: any) => (
                <MobileFormGroup
                  key={field.name}
                  label={`${field.label}${field.autoPopulate && customer ? ' (auto)' : ''}`}
                >
                  <input
                    type="text"
                    className="mobile-form-input"
                    value={formData[field.name] || ''}
                    onChange={(e) => handleFieldChange(field.name, e.target.value)}
                    disabled={generating}
                    placeholder={`Vul ${field.label.toLowerCase()} in`}
                  />
                </MobileFormGroup>
              ))}
            </div>
          </div>
        )}

        {/* Installation Type Tab */}
        {activeTab === 'installation' && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4">Type Installatie</h4>
            <div className="space-y-3">
              {structure.installationTypes.map((type: any) => (
                <div key={type.name} className="flex items-center">
                  <input
                    type="checkbox"
                    id={type.name}
                    className="form-checkbox mobile-touch-target"
                    checked={formData[type.name] || false}
                    onChange={(e) => handleFieldChange(type.name, e.target.checked)}
                    disabled={generating}
                  />
                  <label htmlFor={type.name} className="ml-3 text-amspm-text dark:text-dark-text">
                    {type.label}
                  </label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Inspection Tab */}
        {activeTab === 'inspection' && (
          <div className="space-y-6">
            <h4 className="text-lg font-medium text-amspm-text mb-4">Inspectie Componenten</h4>
            {structure.sections.map((section: any) => (
              <div key={section.title} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h5 className="text-md font-medium text-amspm-text mb-4">{section.title}</h5>
                <div className="overflow-x-auto">
                  <table className="min-w-full table-auto">
                    <thead>
                      <tr className="bg-gray-50 dark:bg-gray-800">
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Component</th>
                        <th className="px-4 py-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300">Goed</th>
                        <th className="px-4 py-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300">Fout</th>
                        <th className="px-4 py-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300">NVT</th>
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Opmerkingen</th>
                      </tr>
                    </thead>
                    <tbody>
                      {section.components.map((component: any) => (
                        <tr key={component.name} className="border-t border-gray-200 dark:border-gray-700">
                          <td className="px-4 py-3 text-sm text-amspm-text dark:text-dark-text">
                            {component.label}
                          </td>
                          <td className="px-4 py-3 text-center">
                            <input
                              type="radio"
                              name={component.name}
                              className="radio radio-success"
                              checked={formData[`${component.name}_goed`] || false}
                              onChange={() => handleRadioGroupChange(component.name, 'goed')}
                              disabled={generating}
                            />
                          </td>
                          <td className="px-4 py-3 text-center">
                            <input
                              type="radio"
                              name={component.name}
                              className="radio radio-error"
                              checked={formData[`${component.name}_fout`] || false}
                              onChange={() => handleRadioGroupChange(component.name, 'fout')}
                              disabled={generating}
                            />
                          </td>
                          <td className="px-4 py-3 text-center">
                            <input
                              type="radio"
                              name={component.name}
                              className="radio radio-warning"
                              checked={formData[`${component.name}_nvt`] || false}
                              onChange={() => handleRadioGroupChange(component.name, 'nvt')}
                              disabled={generating}
                            />
                          </td>
                          <td className="px-4 py-3">
                            <input
                              type="text"
                              className="input input-bordered input-sm w-full"
                              value={formData[`${component.name}_opmerkingen`] || ''}
                              onChange={(e) => handleFieldChange(`${component.name}_opmerkingen`, e.target.value)}
                              disabled={generating}
                              placeholder="Opmerkingen..."
                            />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* General State Tab */}
        {activeTab === 'state' && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-amspm-text mb-4">Algemene Staat</h4>
            <div className="space-y-4">
              {structure.generalState.map((field: any) => (
                <div key={field.name} className="form-control">
                  {field.type === 'textarea' ? (
                    <>
                      <label className="label">
                        <span className="label-text text-amspm-text dark:text-dark-text">
                          {field.label}
                        </span>
                      </label>
                      <textarea
                        className="textarea textarea-bordered w-full"
                        value={formData[field.name] || ''}
                        onChange={(e) => handleFieldChange(field.name, e.target.value)}
                        disabled={generating}
                        placeholder={`Vul ${field.label.toLowerCase()} in`}
                        rows={3}
                      />
                    </>
                  ) : (
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id={field.name}
                        className="checkbox checkbox-primary"
                        checked={formData[field.name] || false}
                        onChange={(e) => handleFieldChange(field.name, e.target.checked)}
                        disabled={generating}
                      />
                      <label htmlFor={field.name} className="ml-3 text-amspm-text dark:text-dark-text">
                        {field.label}
                      </label>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Signatures Tab */}
        {activeTab === 'signatures' && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-amspm-text mb-4">Akkoord & Handtekeningen</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h5 className="text-md font-medium text-amspm-text">Opdrachtgever</h5>
                {structure.signatures.filter((field: any) => field.name.includes('klant') || field.name === 'datum').map((field: any) => (
                  <div key={field.name} className="form-control">
                    {field.type === 'signature' ? (
                      <SignaturePad
                        label={field.label}
                        value={formData[field.name] || ''}
                        onChange={(signature) => handleFieldChange(field.name, signature)}
                        disabled={generating}
                        width={350}
                        height={150}
                      />
                    ) : (
                      <>
                        <label className="label">
                          <span className="label-text text-amspm-text dark:text-dark-text">
                            {field.label}
                          </span>
                        </label>
                        <input
                          type={field.type}
                          className="input input-bordered w-full"
                          value={formData[field.name] || ''}
                          onChange={(e) => handleFieldChange(field.name, e.target.value)}
                          disabled={generating}
                          placeholder={`Vul ${field.label.toLowerCase()} in`}
                        />
                      </>
                    )}
                  </div>
                ))}
              </div>
              <div className="space-y-4">
                <h5 className="text-md font-medium text-amspm-text">Monteur</h5>
                {structure.signatures.filter((field: any) => field.name.includes('monteur') || field.name.includes('tijd')).map((field: any) => (
                  <div key={field.name} className="form-control">
                    {field.type === 'signature' ? (
                      <SignaturePad
                        label={field.label}
                        value={formData[field.name] || ''}
                        onChange={(signature) => handleFieldChange(field.name, signature)}
                        disabled={generating}
                        width={350}
                        height={150}
                      />
                    ) : (
                      <>
                        <label className="label">
                          <span className="label-text text-amspm-text dark:text-dark-text">
                            {field.label}
                          </span>
                        </label>
                        <input
                          type={field.type}
                          className="input input-bordered w-full"
                          value={formData[field.name] || ''}
                          onChange={(e) => handleFieldChange(field.name, e.target.value)}
                          disabled={generating}
                          placeholder={`Vul ${field.label.toLowerCase()} in`}
                        />
                      </>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Mobile Action Buttons */}
      {isMobile && (
        <MobileFormActions>
          <MobileButtonGroup direction="vertical">
            <button
              onClick={handleSave}
              className="btn btn-primary mobile-touch-target"
              disabled={generating}
            >
              {generating ? (
                <><FaSpinner className="animate-spin mr-2" /> Opslaan</>
              ) : (
                <><FaSave className="mr-2" /> Opslaan</>
              )}
            </button>
            <button
              onClick={handleDownload}
              className="btn btn-outline mobile-touch-target"
              disabled={generating}
            >
              <FaDownload className="mr-2" /> Download
            </button>
            <button
              onClick={onCancel}
              className="btn btn-outline mobile-touch-target"
              disabled={generating}
            >
              Annuleren
            </button>
          </MobileButtonGroup>
        </MobileFormActions>
      )}
    </MobileCard>
  );
};

export default InspectionTemplateForm;
