#!/usr/bin/env python3
"""
Pre-deployment checklist script.
Run this before uploading to GitHub to ensure everything is secure.
"""

import os
import json
import sys
from pathlib import Path

def check_sensitive_files():
    """Check for sensitive files that are tracked by git."""
    print("🔍 Checking for sensitive files tracked by git...")

    # Check if git is initialized
    if not os.path.exists('.git'):
        print("⚠️  Git not initialized. Checking file existence instead...")
        return check_sensitive_files_existence()

    # Get list of files tracked by git
    import subprocess
    try:
        result = subprocess.run(['git', 'ls-files'], capture_output=True, text=True, check=True)
        tracked_files = set(result.stdout.strip().split('\n')) if result.stdout.strip() else set()
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Could not run git command. Checking file existence instead...")
        return check_sensitive_files_existence()

    # Sensitive file patterns to check
    sensitive_patterns = [
        'backend/.env',
        'frontend/.env',
        '.env',
        'backend/secrets/',
        'backend/certs/',
        'frontend/certs/',
        'firebase-service-account',
        '.pem',
        '.key',
        '.crt'
    ]

    found_sensitive = []

    for tracked_file in tracked_files:
        for pattern in sensitive_patterns:
            if pattern in tracked_file:
                found_sensitive.append(tracked_file)
                break

    if found_sensitive:
        print("❌ CRITICAL: Found sensitive files tracked by git:")
        for file in found_sensitive:
            print(f"  - {file}")
        print("\nRun these commands to remove them:")
        for file in found_sensitive:
            print(f"  git rm --cached {file}")
        print("  git commit -m 'Remove sensitive files'")
        return False

    print("✅ No sensitive files tracked by git")
    return True

def check_sensitive_files_existence():
    """Fallback: Check if sensitive files exist (when git is not available)."""
    sensitive_files = [
        'backend/.env',
        'frontend/.env',
        '.env'
    ]

    found_files = []
    for file_path in sensitive_files:
        if os.path.exists(file_path):
            found_files.append(file_path)

    if found_files:
        print("⚠️  Found sensitive files (ensure they're in .gitignore):")
        for file in found_files:
            print(f"  - {file}")
        print("These files exist but should be protected by .gitignore")

    # Always return True for existence check since .gitignore should protect them
    return True

def check_gitignore():
    """Check if .gitignore exists and has required entries."""
    print("\n🛡️  Checking .gitignore...")
    
    if not os.path.exists('.gitignore'):
        print("❌ .gitignore file not found!")
        return False
    
    with open('.gitignore', 'r') as f:
        gitignore_content = f.read()
    
    required_entries = [
        '.env',
        'secrets/',
        'firebase-service-account',
        '*.pem',
        '*.key',
        'logs/',
        'certs/'
    ]
    
    missing_entries = []
    for entry in required_entries:
        if entry not in gitignore_content:
            missing_entries.append(entry)
    
    if missing_entries:
        print("⚠️  Missing .gitignore entries:")
        for entry in missing_entries:
            print(f"  - {entry}")
    else:
        print("✅ .gitignore properly configured")
    
    return len(missing_entries) == 0

def check_firebase_config():
    """Check Firebase configuration."""
    print("\n🔥 Checking Firebase configuration...")
    
    # Check if service account file exists
    service_account_path = 'backend/secrets/firebase-service-account-key.json'
    if not os.path.exists(service_account_path):
        print("❌ Firebase service account file not found!")
        print(f"Expected location: {service_account_path}")
        return False
    
    # Validate service account JSON
    try:
        with open(service_account_path, 'r') as f:
            service_account = json.load(f)
        
        required_fields = ['type', 'project_id', 'private_key', 'client_email']
        missing_fields = [field for field in required_fields if field not in service_account]
        
        if missing_fields:
            print(f"❌ Missing fields in service account: {missing_fields}")
            return False
        
        print(f"✅ Firebase service account valid (Project: {service_account.get('project_id')})")
        
    except json.JSONDecodeError:
        print("❌ Invalid JSON in Firebase service account file")
        return False
    except Exception as e:
        print(f"❌ Error reading Firebase service account: {e}")
        return False
    
    return True

def check_required_files():
    """Check if all required deployment files exist."""
    print("\n📁 Checking required deployment files...")
    
    required_files = [
        'backend/app.py',
        'backend/requirements.txt',
        'backend/Procfile',
        'backend/runtime.txt',
        'frontend/package.json',
        'frontend/firebase.json',
        'frontend/.firebaserc',
        'render.yaml'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    return True

def check_environment_templates():
    """Check if environment templates exist."""
    print("\n⚙️  Checking environment templates...")
    
    template_files = [
        'backend/.env.production',
        'frontend/.env.production'
    ]
    
    for file_path in template_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ⚠️  {file_path} not found (optional)")
    
    return True

def check_package_files():
    """Check package configuration files."""
    print("\n📦 Checking package files...")
    
    # Check backend requirements.txt
    if os.path.exists('backend/requirements.txt'):
        with open('backend/requirements.txt', 'r') as f:
            requirements = f.read()
        
        required_packages = ['flask', 'gunicorn', 'psycopg2-binary', 'firebase-admin']
        missing_packages = []
        
        for package in required_packages:
            if package not in requirements:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"⚠️  Missing packages in requirements.txt: {missing_packages}")
        else:
            print("  ✅ Backend requirements.txt looks good")
    
    # Check frontend package.json
    if os.path.exists('frontend/package.json'):
        try:
            with open('frontend/package.json', 'r') as f:
                package_json = json.load(f)
            
            if 'build' in package_json.get('scripts', {}):
                print("  ✅ Frontend package.json has build script")
            else:
                print("  ⚠️  Frontend package.json missing build script")
                
        except json.JSONDecodeError:
            print("  ❌ Invalid JSON in frontend/package.json")
            return False
    
    return True

def main():
    """Run all pre-deployment checks."""
    print("🚀 Pre-Deployment Security Check")
    print("=" * 50)
    
    checks = [
        ("Sensitive Files", check_sensitive_files),
        ("GitIgnore Configuration", check_gitignore),
        ("Firebase Configuration", check_firebase_config),
        ("Required Files", check_required_files),
        ("Environment Templates", check_environment_templates),
        ("Package Files", check_package_files)
    ]
    
    passed = 0
    total = len(checks)
    critical_failed = False
    
    for name, check_func in checks:
        print(f"\n{name}:")
        try:
            if check_func():
                passed += 1
            else:
                if name in ["Sensitive Files", "GitIgnore Configuration"]:
                    critical_failed = True
        except Exception as e:
            print(f"❌ Error in {name}: {e}")
            if name in ["Sensitive Files", "GitIgnore Configuration"]:
                critical_failed = True
    
    print("\n" + "=" * 50)
    print(f"📊 Pre-Deployment Check Results: {passed}/{total} passed")
    
    if critical_failed:
        print("❌ CRITICAL ISSUES FOUND! Do NOT upload to GitHub until fixed.")
        print("\nCritical issues must be resolved before deployment:")
        print("1. Remove all sensitive files")
        print("2. Ensure .gitignore is properly configured")
        return 1
    elif passed == total:
        print("✅ All checks passed! Ready to upload to GitHub.")
        print("\nNext steps:")
        print("1. git add .")
        print("2. git commit -m 'Prepare for production deployment'")
        print("3. git push origin main")
        print("4. Follow STEP_BY_STEP_DEPLOYMENT.md")
        return 0
    else:
        print("⚠️  Some non-critical issues found. Review and fix if needed.")
        print("You can proceed with deployment, but consider fixing the warnings.")
        return 0

if __name__ == "__main__":
    sys.exit(main())
