"""
Custom Rate Limiter Implementation

This module provides a custom rate limiting system that replaces Flask-Limiter
to avoid conflicts between global and endpoint-specific rate limits.

Features:
- Sliding window rate limiting
- Per-endpoint rate limits without global interference
- Memory-based storage (can be extended to Redis)
- Clear error messages and logging
- Thread-safe implementation
"""

import time
import threading
from collections import defaultdict, deque
from functools import wraps
from flask import request, jsonify, g, current_app
import logging
from typing import Dict, Tuple, Optional
import re

logger = logging.getLogger(__name__)


class RateLimitExceeded(Exception):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(self, message: str, retry_after: int = None):
        self.message = message
        self.retry_after = retry_after
        super().__init__(self.message)


class SlidingWindowRateLimiter:
    """
    Sliding window rate limiter implementation.
    
    Uses a sliding window approach to track requests over time periods.
    Thread-safe and memory efficient.
    """
    
    def __init__(self):
        self._windows: Dict[str, deque] = defaultdict(deque)
        self._lock = threading.RLock()
    
    def _parse_limit(self, limit_string: str) -> Tuple[int, int]:
        """
        Parse rate limit string like '60/minute' or '100/hour'.
        
        Args:
            limit_string: Rate limit in format 'count/period'
            
        Returns:
            Tuple of (count, seconds)
        """
        pattern = r'^(\d+)/(second|minute|hour|day)$'
        match = re.match(pattern, limit_string.lower())
        
        if not match:
            raise ValueError(f"Invalid rate limit format: {limit_string}")
        
        count = int(match.group(1))
        period = match.group(2)
        
        period_seconds = {
            'second': 1,
            'minute': 60,
            'hour': 3600,
            'day': 86400
        }
        
        return count, period_seconds[period]
    
    def _get_client_key(self) -> str:
        """
        Generate a unique key for the current client.
        
        Returns:
            Client identifier string
        """
        # Use IP address as the primary identifier
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        
        # If behind a proxy, get the first IP
        if ',' in client_ip:
            client_ip = client_ip.split(',')[0].strip()
        
        # Add user info if available for better granularity
        user_id = getattr(g, 'current_user_id', None)
        if user_id:
            return f"{client_ip}:{user_id}"
        
        return client_ip
    
    def is_allowed(self, limit_string: str, endpoint: str) -> Tuple[bool, Optional[int]]:
        """
        Check if the current request is allowed based on rate limit.
        
        Args:
            limit_string: Rate limit in format 'count/period'
            endpoint: Endpoint identifier for separate tracking
            
        Returns:
            Tuple of (is_allowed, retry_after_seconds)
        """
        try:
            count, window_seconds = self._parse_limit(limit_string)
        except ValueError as e:
            logger.error(f"Invalid rate limit format: {e}")
            return True, None  # Allow request if limit format is invalid
        
        client_key = self._get_client_key()
        window_key = f"{client_key}:{endpoint}"
        current_time = time.time()
        
        with self._lock:
            # Get or create window for this client/endpoint combination
            window = self._windows[window_key]
            
            # Remove expired entries from the sliding window
            cutoff_time = current_time - window_seconds
            while window and window[0] <= cutoff_time:
                window.popleft()
            
            # Check if we're within the limit
            if len(window) >= count:
                # Calculate retry after time
                oldest_request = window[0]
                retry_after = int(oldest_request + window_seconds - current_time) + 1
                return False, retry_after
            
            # Add current request to window
            window.append(current_time)
            
            return True, None
    
    def cleanup_expired_windows(self):
        """
        Clean up expired windows to prevent memory leaks.
        Should be called periodically.
        """
        current_time = time.time()
        expired_keys = []
        
        with self._lock:
            for key, window in self._windows.items():
                # If window is empty or all entries are older than 1 day, mark for deletion
                if not window or (window and current_time - window[-1] > 86400):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._windows[key]
        
        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired rate limit windows")


# Global rate limiter instance
rate_limiter = SlidingWindowRateLimiter()


def rate_limit(limit_string: str):
    """
    Decorator to apply rate limiting to a Flask route.
    
    Args:
        limit_string: Rate limit in format 'count/period' (e.g., '60/minute')
        
    Usage:
        @rate_limit('60/minute')
        def my_endpoint():
            return jsonify({'message': 'Hello'})
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Check if rate limiting is enabled
            if not current_app.config.get('RATE_LIMIT_ENABLED', True):
                logger.debug("Rate limiting is disabled")
                return f(*args, **kwargs)

            # Generate endpoint identifier
            endpoint = f"{request.endpoint or f.__name__}"

            # Check rate limit
            is_allowed, retry_after = rate_limiter.is_allowed(limit_string, endpoint)
            
            if not is_allowed:
                logger.warning(
                    f"Rate limit exceeded for {rate_limiter._get_client_key()} "
                    f"on endpoint {endpoint} (limit: {limit_string})"
                )
                
                response = jsonify({
                    'error': 'Rate limit exceeded',
                    'message': f'Too many requests. Limit: {limit_string}',
                    'retry_after': retry_after
                })
                response.status_code = 429
                
                if retry_after:
                    response.headers['Retry-After'] = str(retry_after)
                
                return response
            
            # Log successful rate limit check (debug level)
            logger.debug(f"Rate limit check passed for {endpoint} (limit: {limit_string})")
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def cleanup_rate_limiter():
    """
    Cleanup function to remove expired rate limit windows.
    Should be called periodically (e.g., via a background task).
    """
    rate_limiter.cleanup_expired_windows()
