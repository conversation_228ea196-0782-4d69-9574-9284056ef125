# Render Environment Variables Setup Guide

## Critical Environment Variables Missing

Your deployment is failing because these **required** environment variables are not set in your Render dashboard:

### 🔥 Firebase Configuration (REQUIRED)

1. **FIREBASE_CREDENTIALS_JSON**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Select your project (`amspmdeploy`)
   - Go to Project Settings → Service Accounts
   - Click "Generate new private key"
   - Copy the entire JSON content
   - In Render dashboard, paste this JSON as **ONE LINE** (no line breaks)

2. **FIREBASE_STORAGE_BUCKET**
   - Value: `amspmdeploy.firebasestorage.app`

### 🌐 CORS Configuration (REQUIRED)

3. **FRONTEND_URL**
   - Value: `https://amspmdeploy.web.app`

4. **FIREBASE_HOSTING_DOMAIN**
   - Value: `https://amspmdeploy.web.app`

### 👤 Admin User (RECOMMENDED)

5. **ADMIN_EMAIL**
   - Value: Your email address for the initial admin user

## How to Set Environment Variables in Render

1. Go to your Render dashboard
2. Select your web service (`customer-management-api`)
3. Go to the **Environment** tab
4. Click **Add Environment Variable**
5. Add each variable listed above

## Quick Fix Commands

After setting the environment variables, your app should redeploy automatically. If not:

1. Go to your service in Render dashboard
2. Click **Manual Deploy** → **Deploy latest commit**

## Verification

Once deployed, check:
- Health endpoint: `https://your-service-url.onrender.com/api/health`
- Should return: `{"status": "healthy", "firebase_initialized": true}`

## Troubleshooting

If you still see errors:

1. **Check logs** in Render dashboard under "Logs" tab
2. **Verify JSON format** - Firebase credentials must be valid JSON on one line
3. **Check Firebase project** - Ensure the project ID matches your setup

## Firebase Service Account JSON Format

Your Firebase credentials should look like this (as ONE LINE):
```json
{"type":"service_account","project_id":"amspmdeploy","private_key_id":"***","private_key":"-----BEGIN PRIVATE KEY-----\n***\n-----END PRIVATE KEY-----\n","client_email":"***","client_id":"***","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"***"}
```

## Next Steps After Fixing

1. Set up the environment variables above
2. Wait for automatic redeployment
3. Check the health endpoint
4. If successful, you can then set up your admin user by accessing the app
