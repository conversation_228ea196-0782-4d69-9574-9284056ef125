import React, { ReactNode, useEffect, useRef } from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showCloseButton?: boolean;
  closeOnClickOutside?: boolean;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnClickOutside = true,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle ESC key press to close modal
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
      // Prevent body scrolling when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  // Determine modal width based on size prop
  const sizeClasses = {
    sm: 'w-full max-w-[95%] sm:max-w-sm',
    md: 'w-full max-w-[95%] sm:max-w-md',
    lg: 'w-full max-w-[95%] sm:max-w-lg',
    xl: 'w-full max-w-[95%] sm:max-w-xl',
  };

  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (closeOnClickOutside && e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 mobile-modal-overlay flex items-center justify-center md:p-4"
      onClick={handleBackdropClick}
      style={{ pointerEvents: 'auto' }}
    >
      <div
        ref={modalRef}
        className={`mobile-modal-content md:modal-content md:${sizeClasses[size]} bg-white dark:bg-dark-secondary shadow-xl md:rounded-lg`}
        onClick={(e) => e.stopPropagation()}
        style={{ pointerEvents: 'auto' }}
      >
        <div className="flex justify-between items-center mb-4 sm:mb-6 border-b border-gray-200 dark:border-gray-700 pb-2 sm:pb-3 pt-6 md:pt-4 px-0 md:px-0">
          <h2 className="text-lg sm:text-xl font-semibold text-amspm-primary dark:text-dark-accent uppercase truncate pr-2">
            {title}
          </h2>
          {showCloseButton && (
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onClose();
              }}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xl transition-colors duration-200 mobile-touch-target flex items-center justify-center rounded hover:bg-gray-100 dark:hover:bg-gray-700"
              aria-label="Close"
              style={{ pointerEvents: 'auto', cursor: 'pointer' }}
            >
              &times;
            </button>
          )}
        </div>
        <div className="pb-4 sm:pb-6">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
